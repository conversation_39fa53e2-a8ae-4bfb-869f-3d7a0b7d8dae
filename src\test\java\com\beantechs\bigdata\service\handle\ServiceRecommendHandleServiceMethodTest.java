package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioTagServiceRecommendationDo;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendReq;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendScenarioTag;
import com.beantechs.bigdata.service.entity.resp.ServiceRecommendResp;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import com.beantechs.service.ResBody.ResponseEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class ServiceRecommendHandleServiceMethodTest {

    @Mock
    private DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;

    @Mock
    private JedisPool jedisPool;

    @Mock
    private Jedis jedis;

    @Mock
    private AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    @InjectMocks
    private ServiceRecommendHandle serviceRecommendHandle;

    private ScenarioRecommendReq buildBaseReq() {
        ScenarioRecommendReq req = new ScenarioRecommendReq();
        req.setVin("VIN123");
        req.setBeanId("BEAN123");

        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");
        req.setTagList(tagList);

        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(Collections.singletonList("S000001"));
        scenarioTagList.add(scenarioTag);
        req.setScenarioTagList(scenarioTagList);

        req.setExecutedScenarioCode("");
        req.setExtend(new JSONObject());
        return req;
    }

    private List<String> buildRedisScenarioList() {
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        List<String> notEmptyRedisCollect = new ArrayList<>();
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));
        return notEmptyRedisCollect;
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(jedisPool.getResource()).thenReturn(jedis);
    }

    @Test
    void testServiceRecommendHandle_success() {
        // given
        ScenarioRecommendReq req = buildBaseReq();

        // 1st mget: scenario list JSON; 2nd mget: today feedback p-values
        when(jedis.mget(any(String[].class)))
                .thenReturn(buildRedisScenarioList())
                .thenReturn(Collections.singletonList("0.10"));
        // hmget: scenario level info
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(Collections.singletonList("300"));
        // smembers: executed set (must be mutable)
        when(jedis.smembers(anyString())).thenReturn(new HashSet<>());

        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // when
        ResponseEntity<ServiceRecommendResp> resp = serviceRecommendHandle.serviceRecommendHandle(req);

        // then
        assertNotNull(resp);
        verify(asyncExecuteThreadPoolHandle, times(1))
                .saveExecutedScenario2Redis(eq("VIN123"), eq("BEAN123"), eq(""));
    }

    @Test
    void testServiceRecommendHandle_redisFailure_returnsFallback() {
        // given
        ScenarioRecommendReq req = buildBaseReq();

        // Throw at mget to trigger fallback branch
        when(jedis.mget(any(String[].class))).thenThrow(new RuntimeException("redis down"));

        // when
        ResponseEntity<ServiceRecommendResp> resp = serviceRecommendHandle.serviceRecommendHandle(req);

        // then
        assertNotNull(resp);
        // async call may still be invoked at end only in success path; here no verify
    }

    @Test
    void testServiceRecommendHandle_emptyTagList_returnsError() {
        // given
        ScenarioRecommendReq req = buildBaseReq();
        List<String> emptyList = new ArrayList<>();
        req.setTagList(emptyList); // empty

        // when
        ResponseEntity<ServiceRecommendResp> resp = serviceRecommendHandle.serviceRecommendHandle(req);

        // then
        assertNotNull(resp);
    }
}

