{"serverUrl": "http://[域名]/[后缀]", "outPath": "src/main/resources/doc", "isStrict": false, "allInOne": true, "createDebugPage": false, "packageFilters": "com.beantechs.bigdata.service.controller.ScenarioPKController", "style": "xt256", "projectName": "smart-audio-service", "showAuthor": true, "allInOneDocFileName": "index.html", "swagger": true, "requestHeaders": [{"name": "key", "type": "string", "desc": "验签key", "value": "验签值", "required": false, "pathPatterns": "/**"}, {"name": "security", "type": "string", "desc": "验签security", "value": "验签值", "required": false, "pathPatterns": "/**"}]}