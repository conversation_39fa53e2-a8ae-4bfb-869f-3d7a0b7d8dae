package com.beantechs.bigdata.service.entity.business;

import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ScenarioInfoBusiness implements Serializable {

    @Column("scenario_id")
    private Integer scenarioId;

    @Column("scenario_code")
    private String scenarioCode;

    @Column("scenario_name")
    private String scenarioName;

    @Column("tree_code")
    private String treeCode;

    @Column("tree_name")
    private String treeName;

    @Column("trigger_types")
    private String triggerTypes;

    @Column("trigger_num")
    private Integer triggerNum;

    @Column("scenario_state")
    private Integer scenarioState;

    @Column("version")
    private String version;

    @Column("publish_date")
    private Date publishDate;

    @Column("validity_start_date")
    private Date validityStartDate;

    @Column("validity_end_date")
    private Date validityEndDate;

    @Column("cal_amount")
    private double calAmount;

    @Column("scenario_price")
    private BigDecimal scenarioPrice;

    @Column("category")
    private BigDecimal category;


}
