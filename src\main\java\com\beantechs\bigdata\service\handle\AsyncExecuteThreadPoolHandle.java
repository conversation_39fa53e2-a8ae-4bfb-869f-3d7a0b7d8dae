package com.beantechs.bigdata.service.handle;

import com.beantechs.bigdata.service.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@EnableAsync
public class AsyncExecuteThreadPoolHandle {

    private final JedisPool jedisPool;

    /**
     * redis库:8
     */
    @Value("${redis.database}")
    private Integer indexDb;


    public AsyncExecuteThreadPoolHandle(
            JedisPool jedisPool
    ) {
        this.jedisPool = jedisPool;
    }


    @Async
    public void saveExecutedScenario2Redis(String vin, String beanId, String code) {
        if (StringUtils.isNotBlank(code)) {
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            try {
                Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
                jedis.sadd("SMART_AUDIO:EXECUTED:TREECODE:" + vin + ":" + beanId + ":" + date, code);
                jedis.expire("SMART_AUDIO:EXECUTED:TREECODE:" + vin + ":" + beanId + ":" + date, (long) 86400);
                jedis.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("保存redis已执行过滤信息异常! key:{}, value:{}", "SMART_AUDIO:EXECUTED:TREECODE:" + vin + ":" + beanId + ":" + date, code);
            }
        }
    }


}
