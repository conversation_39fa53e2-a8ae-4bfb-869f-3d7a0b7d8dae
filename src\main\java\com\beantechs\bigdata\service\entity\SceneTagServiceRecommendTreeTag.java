package com.beantechs.bigdata.service.entity;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SceneTagServiceRecommendTreeTag {

    @NotNull
    private String treeCode;

    @NotNull
    private String scenarioCode;

    private List<String> tagList;

    private Integer tagsSumNum;

    private Integer tagsMatchNum;

    private List<String> tagsMatchList;

    private BigDecimal scenarioLabelScore;

    private BigDecimal labelWeight;

    private BigDecimal pFeedbackLabel;

    private BigDecimal advanceComputeScore;


    private BigDecimal treeRealSortCredit;

    private BigDecimal totalCredit;

    private BigDecimal totalCreditReset;

    private BigDecimal gaussianScore;


    private BigDecimal treeLevelScore;

    private Integer scenarioLevel;


}

