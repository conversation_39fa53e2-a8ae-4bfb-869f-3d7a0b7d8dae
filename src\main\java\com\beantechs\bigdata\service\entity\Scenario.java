package com.beantechs.bigdata.service.entity;

import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class Scenario implements Serializable {

    @Column("id")
    private String id;

    @Column("vin")
    private String vin;

    @Column("tree_code")
    private String treeCode;

    @Column("tree_name")
    private String treeName;

    @Column("scenario_id")
    private Integer scenarioId;

    @Column("scenario_code")
    private String scenarioCode;

    @Column("scenario_name")
    private String scenarioName;

    @Column("trigger_type")
    private String triggerType;

    @Column("trigger_num")
    private Integer triggerNum;

    @Column("feedback_credit")
    private BigDecimal feedbackCredit;

    @Column("scenario_total_credit")
    private BigDecimal scenarioTotalCredit;

    @Column("scenario_dest_credit")
    private BigDecimal scenarioDestCredit;

    @Column("scenario_total_with_avg_credit")
    private BigDecimal scenarioTotalWithAvgCredit;

    @Column("scenario_dest_with_avg_credit")
    private BigDecimal scenarioDestWithAvgCredit;

    @Column("cal_time")
    private String calTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

    public static Scenario of(ScenarioInfo scenarioInfo, String currentSplit, List<ScenarioPkInfo> scenarioPkInfoList) {
        Scenario scenario = new Scenario();
        scenario.setTriggerType(currentSplit);
        scenario.setScenarioId(scenarioInfo.getScenarioId());
        scenario.setScenarioCode(scenarioInfo.getScenarioCode());
        scenario.setScenarioName(scenarioInfo.getScenarioName());
        scenario.setTriggerNum(scenarioInfo.getTriggerNum());
        final ScenarioPkInfo scenarioPKInfo = scenarioPkInfoList.stream().filter(t -> scenarioInfo.getScenarioCode().equals(t.getScenarioCode())).toList().get(0);
        scenario.setScenarioTotalCredit(scenarioPKInfo.getScenarioTotalCredit().setScale(3, RoundingMode.HALF_UP));
        scenario.setScenarioDestCredit(scenarioPKInfo.getScenarioDestCredit().setScale(3, RoundingMode.HALF_UP));
        scenario.setScenarioTotalWithAvgCredit(scenarioPKInfo.getScenarioTotalWithAvgCredit().setScale(3, RoundingMode.HALF_UP));
        scenario.setScenarioDestWithAvgCredit(scenarioPKInfo.getScenarioDestWithAvgCredit().setScale(3, RoundingMode.HALF_UP));
        return scenario;
    }
}
