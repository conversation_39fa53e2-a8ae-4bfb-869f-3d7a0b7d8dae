package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.beantechs.bigdata.service.config.RedisConfig;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioResortBo;
import com.beantechs.bigdata.service.entity.ScenarioTagServiceRecommendationDo;
import com.beantechs.bigdata.service.entity.ServiceRecommendPkStatus;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendReq;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendScenarioTag;
import com.beantechs.bigdata.service.entity.resp.ServiceRecommendResp;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import com.beantechs.service.ResBody.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ServiceRecommendHandle {


    private static final BigDecimal negativeOne = new BigDecimal("-1");
    private static final BigDecimal one = new BigDecimal("2");

    private static final BigDecimal normalWeight = new BigDecimal("0.4");

    private final AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    private final DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;

    private final JedisPool jedisPool;

    private static final Integer indexDb = 8;

    private static final String pkScorePrefixKey = "SMART_AUDIO:PK_SCENARIO_TRIGGER_TYPE_SCORE:";

    private static final String feedbackValuePrefixKey = "SMART_AUDIO:PVALUE:";

    public ServiceRecommendHandle(
            AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle,
            DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService,
            @Qualifier("jedisPool") JedisPool jedisPool
    ) {
        this.asyncExecuteThreadPoolHandle = asyncExecuteThreadPoolHandle;
        this.dataserviceSlaveJdbcTemplateService = dataserviceSlaveJdbcTemplateService;
        this.jedisPool = jedisPool;
    }


    public ResponseEntity<ServiceRecommendResp> serviceRecommendHandle(ScenarioRecommendReq req) {
        // 当天日期
        String todayStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 获取唯一id
        String requestId = resetRequestId(req.getExtend());

        // 剧本、服务及对应的标签列表
        List<ScenarioRecommendScenarioTag> scenarioTagList = req.getScenarioTagList();
        // 标签列表
        List<String> tagList = req.getTagList();

        if (CollectionUtils.isEmpty(tagList)) {
            return ResponseEntity.responseByErrorMsg("tagList is empty!");
        }


        // 构建响应对象
        ServiceRecommendResp serviceRecommendResp = new ServiceRecommendResp();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", requestId);
        serviceRecommendResp.setVin(req.getVin());
        serviceRecommendResp.setBeanId(req.getBeanId());

        //从redis获取剧本列表信息
        Jedis jedis;
        List<String> redisCollect;
        try {
            jedis = RedisConfig.getResource(jedisPool, indexDb);
            redisCollect = jedis.mget(scenarioTagList.stream().map(item -> pkScorePrefixKey + item.getScenarioCode()).toArray(String[]::new));
        } catch (Exception e) {
            log.error("根据剧本code查询redis缓存中的剧本信息失败, 错误信息:", e);
            return ResponseEntity.responseBySucceedData(cannotFinalSourceHandle(scenarioTagList, requestId, req.getVin(), req.getBeanId()));
        }

        //过滤非空对象，获取可用剧本
        List<String> notEmptyRedisCollect = new ArrayList<>();
        if (!CollectionUtils.isEmpty(redisCollect)) {
            notEmptyRedisCollect = redisCollect.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        if (!CollectionUtils.isEmpty(notEmptyRedisCollect)) {
            // 服务-标签 转换为 剧本-触发器, 标签转换为触发器并合并触发器到tagList以便后续统一PK
            tagList = setTriggerParam(notEmptyRedisCollect, scenarioTagList, validCollect, tagList);
        } else {
            log.warn("redis中未获取到剧本信用分，redis key:{}, 入参剧本列表:{}", pkScorePrefixKey, scenarioTagList.stream().map(ScenarioRecommendScenarioTag::getScenarioCode).collect(Collectors.toList()));
            return ResponseEntity.responseBySucceedData(cannotFinalSourceHandle(scenarioTagList, requestId, req.getVin(), req.getBeanId()));
        }

        // 设置PK时的参数信息
        setLabelNumPKParam(tagList, validCollect);

        validCollect = validCollect.stream().filter(o -> !CollectionUtils.isEmpty(o.getTagsMatchList())).toList();

        //有效的剧本code列表
        List<String> validScenarioCodeList = validCollect.stream().map(ServiceRecommendPkStatus::getScenarioCode).collect(Collectors.toList());

        // 定义需查询反馈分redis key的大小
        List<String> currentKeyList = new ArrayList<>(validScenarioCodeList.size());

        // 设置反馈分的查询key
        queryTodayFeedBack(req.getVin(), req.getBeanId(), validScenarioCodeList, currentKeyList, todayStr);

        // 根据剧本codes取对应用户反馈分
        List<String> currentValues = new ArrayList<>();
        try {
            currentValues = jedis.mget(currentKeyList.toArray(new String[0]));
        } catch (Exception e) {
            log.error("查询用户实时反馈分失败, 错误信息:", e);
        }

        //实时反馈重排
        realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);
        //实时剧本等级重排
        realReSortLevelValue(validCollect, validScenarioCodeList, jedis);

        for (int i = 0; i < validScenarioCodeList.size(); i++) {
            String scenarioCode = validScenarioCodeList.get(i);
            int finalI = i;
            List<String> finalCurrentValues = currentValues;
            validCollect.forEach(obj -> {
                if (obj.getScenarioCode().equals(scenarioCode)) {
                    calFeedbackP(finalCurrentValues, finalI, obj);
                }
            });
        }

        // 对已执行的剧本排序到最后面处理
        List<ServiceRecommendPkStatus> collect = sortAlreadyExecuted(req, jedis, todayStr, validCollect);

        serviceRecommendResp.setRecommendations(collect.stream().map(ServiceRecommendPkStatus::getScenarioCode).distinct().toList());

        // 结果处理, 如果有重复的剧本，取分数高的
        List<JSONObject> scoreList = collect.stream().distinct().map(o -> {
                    JSONObject json = new JSONObject();
                    json.put("scenarioCode", o.getScenarioCode());
                    json.put("score", o.getGaussianScore());
                    json.put("scenarioLevel", o.getScenarioLevel());
                    return json;
                }).collect(Collectors.groupingBy(json -> json.getString("scenarioCode")))
                .values().stream()
                .map(valueList -> valueList.stream()
                        .max(Comparator.comparingDouble(json -> json.getDoubleValue("score")))
                        .orElseThrow(NoSuchElementException::new))
                .toList();
        jsonObject.put("scoreList", scoreList);
        serviceRecommendResp.setExtend(jsonObject);
        asyncExecuteThreadPoolHandle.saveExecutedScenario2Redis(req.getVin(), req.getBeanId(), req.getExecutedScenarioCode());
        return ResponseEntity.responseBySucceedData(serviceRecommendResp);
    }

    private static List<ServiceRecommendPkStatus> sortAlreadyExecuted(ScenarioRecommendReq req, Jedis jedis, String todayStr, List<ServiceRecommendPkStatus> validCollect) {
        Set<String> executedScenario = new HashSet<>();
        try {
            executedScenario = jedis.smembers("SMART_AUDIO:EXECUTED:TREECODE:" + req.getVin() + ":" + req.getBeanId() + ":" + todayStr);
        } catch (Exception e) {
            log.error("查询redis获取已执行列表失败, 错误信息:", e);
        }
        jedis.close();

        executedScenario.add(req.getExecutedScenarioCode());
        List<ServiceRecommendPkStatus> list = new ArrayList<>();
        Set<String> finalExecutedScenario = executedScenario;
        List<ServiceRecommendPkStatus> collect = validCollect.stream()
                .sorted(Comparator.comparing(ServiceRecommendPkStatus::getScenarioLevel).thenComparing(Comparator.comparing(ServiceRecommendPkStatus::getGaussianScore).reversed()))
                .map(item -> {
                    // 将已经执行过的放在队列末尾, list => 已执行过的对象
                    if (finalExecutedScenario.contains(item.getScenarioCode())) {
                        list.add(item);
                    } else {
                        return item;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        collect.addAll(list);
        return collect;
    }

    private static String resetRequestId(JSONObject extend) {
        String requestId;
        if (null != extend) {
            if (StringUtils.isNotBlank(extend.getString("requestId"))) {
                requestId = extend.getString("requestId");
            } else {
                requestId = UUID.randomUUID().toString().replace("-", "");
            }
        } else {
            requestId = UUID.randomUUID().toString().replace("-", "");
        }
        return requestId;
    }

    private static ServiceRecommendResp cannotFinalSourceHandle(List<ScenarioRecommendScenarioTag> scenarioTagList, String requestId, String vin, String beanId) {
        ServiceRecommendResp serviceRecommendResp = new ServiceRecommendResp();
        List<String> collect = new ArrayList<>(scenarioTagList.stream().map(ScenarioRecommendScenarioTag::getScenarioCode).toList());
        Collections.shuffle(collect);
        serviceRecommendResp.setRecommendations(collect);
        serviceRecommendResp.setVin(vin);
        serviceRecommendResp.setBeanId(beanId);

        JSONObject json = new JSONObject();
        json.put("requestId", requestId);
        List<JSONObject> scoreList = new ArrayList<>(scenarioTagList.stream().map(o -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("scenarioCode", o.getScenarioCode());
            jsonObject.put("score", 0.00);
            return jsonObject;
        }).toList());
        json.put("scoreList", scoreList);
        serviceRecommendResp.setExtend(json);
        return serviceRecommendResp;
    }


    private static List<String> setTriggerParam(
            List<String> notEmptyRedisCollect,
            List<ScenarioRecommendScenarioTag> scenarioTagList,
            List<ServiceRecommendPkStatus> validCollect,
            List<String> tagList
    ) {
        //转实体类
        List<List<Scenario>> redisListEntity = notEmptyRedisCollect.stream().map(item -> JSON.parseArray(item, Scenario.class)).toList();
        List<String> triggerList = new ArrayList<>();
        redisListEntity.forEach(obj -> scenarioTagList.forEach(item -> {
            if (item.getScenarioCode().equals(obj.get(0).getScenarioCode())) {
                ServiceRecommendPkStatus serviceRecommendPKStatus = new ServiceRecommendPkStatus();
                serviceRecommendPKStatus.setAdvanceComputeScore(obj.get(0).getScenarioTotalCredit());
                serviceRecommendPKStatus.setScenarioCode(item.getScenarioCode());
                if (CollectionUtils.isEmpty(item.getTagList())) {
                    //如果是触发器剧本而不是标签，重设tagList
                    List<String> triggerTypeList = obj.stream().map(Scenario::getTriggerType).toList();
                    if (!CollectionUtils.isEmpty(triggerTypeList)) {
                        serviceRecommendPKStatus.setTagList(triggerTypeList);
                    } else {
                        serviceRecommendPKStatus.setTagList(tagList);
                    }
                } else {
                    serviceRecommendPKStatus.setTagList(item.getTagList());
                }
                //将可触发的触发器添加到tagList
                Optional<String> first = obj.stream().filter(s -> s.getScenarioCode().equals(item.getScenarioCode())).map(Scenario::getTriggerType).findFirst();
                first.ifPresent(triggerList::add);
                validCollect.add(serviceRecommendPKStatus);
            }
        }));
        //合并触发器
        tagList.addAll(triggerList);
        //去重
        return tagList.stream().distinct().collect(Collectors.toList());
    }


    private static void setLabelNumPKParam(List<String> tagList, List<ServiceRecommendPkStatus> validCollect) {
        for (ServiceRecommendPkStatus item : validCollect) {
            ArrayList<String> strings = new ArrayList<>(tagList);
            strings.retainAll(item.getTagList());
            item.setTagsMatchList(strings);
            item.setTagsMatchNum(strings.size());
            item.setTagsSumNum(strings.size());
            item.setTagList(strings);
        }
    }


    private static void queryTodayFeedBack(String vin, String beanId, List<String> codes, List<String> currentKeyList, String todayStr) {
        codes.forEach(code -> currentKeyList.add(feedbackValuePrefixKey + vin + ScenarioConstants.JAVA_COMMON_MAO_STR + beanId + ScenarioConstants.JAVA_COMMON_MAO_STR + code + ScenarioConstants.JAVA_COMMON_MAO_STR + todayStr));
    }


    public void realResortCore(String vin, String beanId, List<String> tagList, List<ServiceRecommendPkStatus> scenarioPKStatusList) {
        HashMap<String, Integer> tagMatchTimesMap = new HashMap<>();
        tagList.forEach(tag -> scenarioPKStatusList.forEach(scenarioPKStatus -> mergeTagMatchTime(tag, scenarioPKStatus, tagMatchTimesMap)));

        //计算剧本信用值
        scenarioPKStatusList.forEach(scenarioPKStatus -> {
            AtomicReference<BigDecimal> scenarioLabelScore = new AtomicReference<>(BigDecimal.ZERO);
            scenarioPKStatus.getTagList().forEach(tag -> {
                if (tagMatchTimesMap.containsKey(tag)) {
                    BigDecimal matchNum = BigDecimal.valueOf(scenarioPKStatus.getTagList().size());
                    BigDecimal logValue = BigDecimal.valueOf(Math.log((double) (scenarioPKStatusList.size() + 1) / (tagMatchTimesMap.get(tag) + 1)));
                    BigDecimal increment = BigDecimal.ONE.divide(matchNum, 10, RoundingMode.HALF_UP).multiply(logValue).setScale(3, RoundingMode.HALF_UP);
                    scenarioLabelScore.updateAndGet(v -> v.add(increment));
                }
            });

            BigDecimal roundedScenarioLabelScore = scenarioLabelScore.get().setScale(3, RoundingMode.HALF_UP);
            scenarioPKStatus.setScenarioLabelScore(roundedScenarioLabelScore);

            BigDecimal tagsNum = BigDecimal.valueOf(scenarioPKStatus.getTagList().size());
            BigDecimal labelWeight = BigDecimal.ONE.add(BigDecimal.ONE.divide(BigDecimal.ONE.add(BigDecimal.valueOf(Math.exp(-0.8 * (tagsNum.doubleValue() - 6)))), 10, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP);
            scenarioPKStatus.setLabelWeight(labelWeight);

            BigDecimal pFeedbackLabel = labelWeight.multiply(roundedScenarioLabelScore).setScale(3, RoundingMode.HALF_UP);
            scenarioPKStatus.setPFeedbackLabel(pFeedbackLabel);
        });

        //根据标签权重分数排序
        List<String> scenarioCodes = new ArrayList<>();
        List<ServiceRecommendPkStatus> onLineSortList = scenarioPKStatusList.stream().sorted(Comparator.comparing(ServiceRecommendPkStatus::getPFeedbackLabel).reversed()).toList();
        onLineSortList.forEach(item -> scenarioCodes.add(item.getScenarioCode()));

        int tagIndex = 1;
        List<ScenarioResortBo> sortOnLineList = new ArrayList<>();
        for (int i = 0; i < onLineSortList.size(); i++) {
            ServiceRecommendPkStatus tag = onLineSortList.get(i);
            int rank = tagIndex;

            if (i > 0 && tag.getPFeedbackLabel().equals(onLineSortList.get(i - 1).getPFeedbackLabel())) {
                rank = sortOnLineList.get(i - 1).getRank();
            }
            sortOnLineList.add(new ScenarioResortBo(tag.getScenarioCode(), 1, rank));
            tagIndex++;
        }

        // 获取前一天的日期字符串
        String yesterdayAsString = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        ScenarioTagServiceRecommendationDo combin = dataserviceSlaveJdbcTemplateService.getScenarioOne(vin, beanId, yesterdayAsString);
        List<ScenarioResortBo> sortOffLineList;
        //如果不为空, 重排离线
        if (!Objects.isNull(combin)) {
            sortOffLineList = Arrays.stream(combin.getScenarioCodeRanks().split(","))
                    .map(item -> {
                        String[] split = item.split(":");
                        return new ScenarioResortBo(split[0], Integer.parseInt(split[1]), Integer.parseInt(split[2]));
                    }).filter(item -> scenarioCodes.contains(item.getScenarioCode())).toList();
        } else {
            sortOffLineList = new ArrayList<>();
        }

        //合并重排结果
        List<ServiceRecommendPkStatus> invalidList = scenarioPKStatusList.stream().filter(item -> item.getPFeedbackLabel().compareTo(new BigDecimal("0.00")) == 0).toList();

        List<ScenarioResortBo> scenarioResortBoList = sortOnLineList.stream().peek(item -> {
            Optional<ScenarioResortBo> first = sortOffLineList.stream().filter(bo -> item.getScenarioCode().equals(bo.getScenarioCode())).findFirst();
            if (first.isPresent()) {
                ScenarioResortBo scenarioResortBo = first.get();
                item.setNum(scenarioResortBo.getNum() + item.getNum());
            }
            if (!CollectionUtils.isEmpty(invalidList) && invalidList.stream().map(ServiceRecommendPkStatus::getScenarioCode).toList().contains(item.getScenarioCode())) {
                item.setRank(999);
            } else {
                if (first.isPresent()) {
                    ScenarioResortBo scenarioResortBo = first.get();
                    item.setRank(scenarioResortBo.getRank() + item.getRank());
                }
            }
        }).sorted(Comparator.comparing(ScenarioResortBo::getNum).reversed().thenComparing(ScenarioResortBo::getRank)).toList();


        //最终排序
        for (int i = 1; i <= scenarioResortBoList.size(); i++) {
            ScenarioResortBo scenarioResortBo = scenarioResortBoList.get(i - 1);

            // 使用 BigDecimal 进行计算
            BigDecimal index = BigDecimal.valueOf(scenarioResortBo.getRank());
            BigDecimal twenty = new BigDecimal("20.00");
            BigDecimal pointTwo = new BigDecimal("0.2");
            BigDecimal one = new BigDecimal("1.00");
            BigDecimal pointFive = new BigDecimal("0.50");
            BigDecimal two = new BigDecimal("2.00");

            // 计算 exponent = 0.2 * (i - 20)
            BigDecimal exponent = pointTwo.multiply(index.subtract(twenty));

            // 计算 expValue = Math.pow(Math.E, exponent.doubleValue())
            BigDecimal expValue = BigDecimal.valueOf(Math.exp(exponent.doubleValue()));

            // 计算 denominator = expValue + 1
            BigDecimal denominator = expValue.add(one);

            // 计算 feedback = 2.00 / denominator - 0.50
            BigDecimal feedback = two.divide(denominator, 3, RoundingMode.HALF_UP).subtract(pointFive).setScale(3, RoundingMode.HALF_UP);

            String scenarioCode = scenarioResortBo.getScenarioCode();
            scenarioPKStatusList.forEach(scenarioPKStatus -> {
                if (scenarioResortBo.getRank() == 0 && scenarioResortBo.getNum() == 0) {
                    scenarioPKStatus.setScenarioRealSortCredit(BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP));
                } else {
                    if (scenarioPKStatus.getScenarioCode().equals(scenarioCode)) {
                        scenarioPKStatus.setScenarioRealSortCredit(feedback);
                    }
                }
            });
        }
    }


    public void realReSortLevelValue(List<ServiceRecommendPkStatus> serviceRecommendPkStatusList, List<String> scenarioCodes, Jedis jedis) {
        String[] array = scenarioCodes.toArray(new String[0]);
        List<String> arrayValues = null;
        try {
            arrayValues = jedis.hmget("SMART_AUDIO:SCENARIO:LEVEL:INFO", array);
        } catch (Exception e) {
            log.error("查询剧本等级异常, 错误信息:", e);
        }

        for (ServiceRecommendPkStatus serviceRecommendPKStatus : serviceRecommendPkStatusList) {
            Integer index = null;
            for (int i = 0; i < scenarioCodes.size(); i++) {
                if (scenarioCodes.get(i).equals(serviceRecommendPKStatus.getScenarioCode())) {
                    index = i;
                    break;
                }
            }

            String scenarioLevel;
            if (index == null) {
                scenarioLevel = "300";
            } else {
                if (null == arrayValues) {
                    scenarioLevel = "300";
                } else {
                    if (StringUtils.isNotBlank(arrayValues.get(index))) {
                        scenarioLevel = arrayValues.get(index);
                    } else {
                        scenarioLevel = "300";
                    }
                }

            }
            serviceRecommendPKStatus.setScenarioLevel(Integer.parseInt(null == scenarioLevel ? "300" : scenarioLevel));

            // 使用 BigDecimal 进行计算
            BigDecimal one = new BigDecimal("1.00");
            BigDecimal threeHundred = new BigDecimal("300");
            BigDecimal pointZeroTwo = new BigDecimal("0.02");

            // 计算 exponent = 0.02 * (treeLevel - 300)
            BigDecimal scenarioLevelBD = new BigDecimal(StringUtils.isBlank(scenarioLevel) ? "300" : scenarioLevel);
            BigDecimal exponent = pointZeroTwo.multiply(scenarioLevelBD.subtract(threeHundred));

            // 计算 expValue = Math.pow(Math.E, exponent.doubleValue())
            BigDecimal expValue = BigDecimal.valueOf(Math.exp(exponent.doubleValue()));

            // 计算 denominator = expValue + 1
            BigDecimal denominator = expValue.add(one);

            // 计算 divide = 1.00 / denominator
            BigDecimal divide = one.divide(denominator, 3, RoundingMode.HALF_UP);

            // 更新 tree 的值
            serviceRecommendPKStatus.setScenarioRealSortCredit(serviceRecommendPKStatus.getScenarioRealSortCredit().add(divide));
            serviceRecommendPKStatus.setScenarioLevelScore(divide);
        }
    }


    private static void calFeedbackP(List<String> currentValues, int i, ServiceRecommendPkStatus serviceRecommendPKStatus) {
        BigDecimal feedbackValue = new BigDecimal("0.00");

        if (!Objects.isNull(serviceRecommendPKStatus)) {
            feedbackValue = feedbackValue.add(serviceRecommendPKStatus.getScenarioRealSortCredit());
        }

        if (!CollectionUtils.isEmpty(currentValues)) {
            String currentP = currentValues.get(i);
            if (StringUtils.isNotBlank(currentP)) {
                feedbackValue = feedbackValue.add(TypeUtils.castToBigDecimal(currentP));
            }
        }

        if (feedbackValue.compareTo(negativeOne) < 0) {
            feedbackValue = negativeOne;
        }
        if (feedbackValue.compareTo(one) > 0) {
            feedbackValue = one;
        }

        BigDecimal scenarioTotalCredit = serviceRecommendPKStatus.getAdvanceComputeScore();
        BigDecimal totalCredit = feedbackValue.add(scenarioTotalCredit);
        serviceRecommendPKStatus.setTotalCredit(totalCredit);

        BigDecimal finalTotal = (new BigDecimal("3.8").multiply(totalCredit.subtract(new BigDecimal("0.2")))).divide(new BigDecimal("4.8"), 3, RoundingMode.HALF_UP).add(new BigDecimal("0.2"));
        serviceRecommendPKStatus.setTotalCreditReset(finalTotal);

        BigDecimal gaussian = ServiceRecommendHandle.normalWeight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(finalTotal).setScale(3, RoundingMode.HALF_UP);
        serviceRecommendPKStatus.setGaussianScore(gaussian);
    }


    private static void mergeTagMatchTime(String tag, ServiceRecommendPkStatus scenarioPKStatus, HashMap<String, Integer> tagMatchTimesMap) {
        if (scenarioPKStatus.getTagList().contains(tag)) {
            tagMatchTimesMap.merge(tag, 1, Integer::sum);
        }
    }

}
