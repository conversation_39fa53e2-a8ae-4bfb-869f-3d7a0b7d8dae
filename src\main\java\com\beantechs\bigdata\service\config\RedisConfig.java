package com.beantechs.bigdata.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisConfig {
    @Value("${redis.host}")
    private String redisHost;

    @Value("${redis.port}")
    private Integer redisPort;

    @Value("${redis.blockWhenExhausted}")
    private boolean redisBlockWhenExhausted;

    @Value("${redis.timeout}")
    private Integer redisTimeOut;

    @Value("${redis.password}")
    private String redisPwd;

    @Bean
    @Qualifier("jedisPool")
    public JedisPool redisPoolFactory() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(1000);
        jedisPoolConfig.setMaxIdle(200);
        jedisPoolConfig.setMinIdle(10);
        // 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
        jedisPoolConfig.setJmxEnabled(false);
        jedisPoolConfig.setBlockWhenExhausted(redisBlockWhenExhausted);
        return new JedisPool(jedisPoolConfig, redisHost, redisPort, redisTimeOut, redisPwd);
    }


    public static Jedis getResource(JedisPool jedisPool, Integer indexDb) {
        Jedis jedis = jedisPool.getResource();
        jedis.select(indexDb);
        return jedis;
    }

}
