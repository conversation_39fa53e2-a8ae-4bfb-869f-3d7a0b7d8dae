package com.beantechs.bigdata.service.entity.req;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScenarioRecommendReq {

    @NotBlank
    private String vin;

    @NotBlank
    private String beanId;

    @NotNull
    private List<String> tagList;

    @NotNull
    private List<ScenarioRecommendScenarioTag> scenarioTagList;

    private String executedScenarioCode;

    private JSONObject extend;
}
