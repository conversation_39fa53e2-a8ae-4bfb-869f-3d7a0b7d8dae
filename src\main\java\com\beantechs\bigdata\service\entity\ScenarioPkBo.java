package com.beantechs.bigdata.service.entity;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class ScenarioPkBo {
    private String vin;

    private String beanId;

    private Integer actionSign;

    private List<String> codeList;

    private Map<String, String> oldFeedbackValueMap;

    private Map<String, String> currentFeedbackValueMap;

    private List<Scenario> scenarioList;

    private Map<String, List<Scenario>> triggerTypeMap;

    private Map<String, List<Scenario>> codeMap;

    public ScenarioPkBo(String vin, String beanId, Integer actionSign) {
        this.vin = vin;
        this.beanId = beanId;
        this.actionSign = actionSign;
    }

    public void initOldFeedbackValueMap(List<String> keys, List<String> values) {
        oldFeedbackValueMap = convertMap(keys, values);
    }

    public void initCurrentFeedbackValueMap(List<String> keys, List<String> values) {
        currentFeedbackValueMap = convertMap(keys, values);
    }

    private static Map<String, String> convertMap(List<String> keys, List<String> values) {
        final int size = keys.size();
        Map<String, String> map = Maps.newHashMapWithExpectedSize(size);

        for (int i = 0; i < size; i++) {
            map.put(keys.get(i), values.get(i));
        }
        return map;
    }

    public void initGroupMap() {
        //根据触发器分类
        triggerTypeMap = scenarioList.stream()
                .collect(Collectors.groupingBy(Scenario::getTriggerType));

        //根据剧本Code分类
        codeMap = scenarioList.stream()
                .collect(Collectors.groupingBy(Scenario::getScenarioCode));
    }

}
