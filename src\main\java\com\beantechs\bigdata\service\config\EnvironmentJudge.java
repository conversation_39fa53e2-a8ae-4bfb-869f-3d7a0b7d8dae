package com.beantechs.bigdata.service.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * <AUTHOR>
 */
public class EnvironmentJudge implements Condition {
    @Override
    public boolean matches(ConditionContext context, @NotNull AnnotatedTypeMetadata metadata) {
        String environment = context.getEnvironment().getProperty("environment");
        return !("prd".equals(environment) || "gw-qa".equals(environment));
    }
}
