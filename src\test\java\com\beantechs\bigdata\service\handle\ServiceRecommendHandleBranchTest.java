package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioTagServiceRecommendationDo;
import com.beantechs.bigdata.service.entity.ServiceRecommendPkStatus;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendReq;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendScenarioTag;
import com.beantechs.bigdata.service.entity.resp.ServiceRecommendResp;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import com.beantechs.service.ResBody.ResponseEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ServiceRecommendHandle分支覆盖率测试
 * 专门用于提升分支覆盖率到80%以上
 */
public class ServiceRecommendHandleBranchTest {

    @Mock
    private DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;

    @Mock
    private JedisPool jedisPool;

    @Mock
    private Jedis jedis;

    @Mock
    private AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    @InjectMocks
    private ServiceRecommendHandle serviceRecommendHandle;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(jedisPool.getResource()).thenReturn(jedis);
    }

    // ========== 主方法serviceRecommendHandle的分支测试 ==========

    @Test
    void testServiceRecommendHandle_EmptyRedisCollect() {
        // 测试Redis返回空集合的分支
        ScenarioRecommendReq req = buildBaseReq();
        
        // 模拟Redis返回空集合
        when(jedis.mget(any(String[].class))).thenReturn(new ArrayList<>());
        
        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);
        
        assertNotNull(result);
        assertNotNull(result.getData());
        // 应该返回fallback结果
        assertNotNull(result.getData().getRecommendations());
    }

    @Test
    void testServiceRecommendHandle_RedisCollectWithNullValues() {
        // 测试Redis返回包含null值的集合
        ScenarioRecommendReq req = buildBaseReq();
        
        // 模拟Redis返回包含null的集合
        List<String> redisResult = new ArrayList<>();
        redisResult.add(null);
        redisResult.add(buildValidScenarioJson());
        when(jedis.mget(any(String[].class))).thenReturn(redisResult);
        
        // 模拟其他必要的Redis调用
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(Collections.singletonList("300"));
        when(jedis.smembers(anyString())).thenReturn(new HashSet<>());
        
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);
        
        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);
        
        assertNotNull(result);
        assertNotNull(result.getData());
    }

    @Test
    void testServiceRecommendHandle_EmptyNotEmptyRedisCollect() {
        // 测试过滤后的notEmptyRedisCollect为空的分支
        ScenarioRecommendReq req = buildBaseReq();
        
        // 模拟Redis返回全是null的集合
        List<String> redisResult = Arrays.asList(null, null, null);
        when(jedis.mget(any(String[].class))).thenReturn(redisResult);
        
        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);
        
        assertNotNull(result);
        assertNotNull(result.getData());
        // 应该返回fallback结果
        assertNotNull(result.getData().getRecommendations());
    }

    @Test
    void testServiceRecommendHandle_FeedbackQueryException() {
        // 测试查询反馈分时发生异常的分支
        ScenarioRecommendReq req = buildBaseReq();
        
        // 第一次mget调用返回正常数据
        when(jedis.mget(any(String[].class)))
                .thenReturn(Collections.singletonList(buildValidScenarioJson()))
                .thenThrow(new RuntimeException("Redis feedback query failed"));
        
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(Collections.singletonList("300"));
        when(jedis.smembers(anyString())).thenReturn(new HashSet<>());
        
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);
        
        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);
        
        assertNotNull(result);
        assertNotNull(result.getData());
    }

    @Test
    void testServiceRecommendHandle_FilteredValidCollectEmpty() {
        // 测试过滤后validCollect为空的分支
        ScenarioRecommendReq req = buildBaseReq();
        req.setTagList(Arrays.asList("NONEXISTENT_TAG")); // 设置不匹配的标签
        
        when(jedis.mget(any(String[].class)))
                .thenReturn(Collections.singletonList(buildValidScenarioJson()))
                .thenReturn(Collections.singletonList("0.1"));
        
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(Collections.singletonList("300"));
        when(jedis.smembers(anyString())).thenReturn(new HashSet<>());
        
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);
        
        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);
        
        assertNotNull(result);
        assertNotNull(result.getData());
        // 应该返回空的推荐列表
        assertTrue(result.getData().getRecommendations().isEmpty());
    }

    // ========== sortAlreadyExecuted方法的分支测试 ==========

    @Test
    void testSortAlreadyExecuted_RedisException() {
        // 测试Redis查询已执行剧本时发生异常
        ScenarioRecommendReq req = buildBaseReq();
        List<ServiceRecommendPkStatus> validCollect = buildValidCollectList();
        
        when(jedis.smembers(anyString())).thenThrow(new RuntimeException("Redis smembers failed"));
        
        List<ServiceRecommendPkStatus> result = ReflectionTestUtils.invokeMethod(
                ServiceRecommendHandle.class,
                "sortAlreadyExecuted",
                req, jedis, "2024-01-01", validCollect);
        
        assertNotNull(result);
        // 即使Redis异常，也应该能正常处理
    }

    @Test
    void testSortAlreadyExecuted_WithExecutedScenarios() {
        // 测试包含已执行剧本的分支
        ScenarioRecommendReq req = buildBaseReq();
        req.setExecutedScenarioCode("Ep0EvjY");
        List<ServiceRecommendPkStatus> validCollect = buildValidCollectList();
        
        Set<String> executedSet = new HashSet<>();
        executedSet.add("AnotherScenario");
        when(jedis.smembers(anyString())).thenReturn(executedSet);
        
        List<ServiceRecommendPkStatus> result = ReflectionTestUtils.invokeMethod(
                ServiceRecommendHandle.class,
                "sortAlreadyExecuted",
                req, jedis, "2024-01-01", validCollect);
        
        assertNotNull(result);
        // 已执行的剧本应该被排到后面
    }

    // ========== 辅助方法 ==========

    private ScenarioRecommendReq buildBaseReq() {
        ScenarioRecommendReq req = new ScenarioRecommendReq();
        req.setVin("VIN123");
        req.setBeanId("BEAN123");
        req.setTagList(Arrays.asList("S000001"));
        
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(Arrays.asList("S000001"));
        req.setScenarioTagList(Collections.singletonList(scenarioTag));
        
        req.setExecutedScenarioCode("");
        req.setExtend(new JSONObject());
        return req;
    }

    private String buildValidScenarioJson() {
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        return JSON.toJSONString(Collections.singletonList(scenario));
    }

    private List<ServiceRecommendPkStatus> buildValidCollectList() {
        List<ServiceRecommendPkStatus> list = new ArrayList<>();
        ServiceRecommendPkStatus status = new ServiceRecommendPkStatus();
        status.setScenarioCode("Ep0EvjY");
        status.setTagList(Arrays.asList("S000001"));
        status.setTagsMatchList(Arrays.asList("S000001"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));
        status.setGaussianScore(new BigDecimal("0.8"));
        status.setScenarioLevel(300);
        list.add(status);
        return list;
    }

    // ========== realResortCore方法的分支测试 ==========

    @Test
    void testRealResortCore_CombinIsNull() {
        // 测试combin为null的分支
        String vin = "VIN123";
        String beanId = "BEAN123";
        List<String> tagList = Arrays.asList("S000001", "S000002");
        List<ServiceRecommendPkStatus> scenarioPKStatusList = buildValidCollectList();

        // 设置必要的字段
        scenarioPKStatusList.get(0).setTagsMatchList(Arrays.asList("S000001"));
        scenarioPKStatusList.get(0).setTagsMatchNum(1);

        // 模拟数据库查询返回null
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(null);

        serviceRecommendHandle.realResortCore(vin, beanId, tagList, scenarioPKStatusList);

        // 验证计算结果
        assertNotNull(scenarioPKStatusList.get(0).getScenarioLabelScore());
        assertNotNull(scenarioPKStatusList.get(0).getLabelWeight());
        assertNotNull(scenarioPKStatusList.get(0).getPFeedbackLabel());
    }

    @Test
    void testRealResortCore_InvalidListNotEmpty() {
        // 测试invalidList不为空的分支
        String vin = "VIN123";
        String beanId = "BEAN123";
        List<String> tagList = Arrays.asList("S000001");
        List<ServiceRecommendPkStatus> scenarioPKStatusList = new ArrayList<>();

        // 创建一个pFeedbackLabel为0的状态（会被加入invalidList）
        ServiceRecommendPkStatus invalidStatus = new ServiceRecommendPkStatus();
        invalidStatus.setScenarioCode("InvalidScenario");
        invalidStatus.setTagList(Arrays.asList("S000001"));
        invalidStatus.setTagsMatchList(Arrays.asList("S000001"));
        invalidStatus.setTagsMatchNum(1);
        invalidStatus.setPFeedbackLabel(new BigDecimal("0.00")); // 这会导致被加入invalidList
        scenarioPKStatusList.add(invalidStatus);

        // 创建一个正常的状态
        ServiceRecommendPkStatus normalStatus = buildValidCollectList().get(0);
        normalStatus.setTagsMatchList(Arrays.asList("S000001"));
        normalStatus.setTagsMatchNum(1);
        scenarioPKStatusList.add(normalStatus);

        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2,InvalidScenario:1:3");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        serviceRecommendHandle.realResortCore(vin, beanId, tagList, scenarioPKStatusList);

        // 验证invalidList中的项目rank被设置为999
        // 这个测试主要验证分支逻辑
        assertNotNull(scenarioPKStatusList.get(0).getScenarioRealSortCredit());
    }

    @Test
    void testRealResortCore_EqualPFeedbackLabel() {
        // 测试相同pFeedbackLabel的排序分支
        String vin = "VIN123";
        String beanId = "BEAN123";
        List<String> tagList = Arrays.asList("S000001");
        List<ServiceRecommendPkStatus> scenarioPKStatusList = new ArrayList<>();

        // 创建两个具有相同pFeedbackLabel的状态
        for (int i = 0; i < 2; i++) {
            ServiceRecommendPkStatus status = new ServiceRecommendPkStatus();
            status.setScenarioCode("Scenario" + i);
            status.setTagList(Arrays.asList("S000001"));
            status.setTagsMatchList(Arrays.asList("S000001"));
            status.setTagsMatchNum(1);
            scenarioPKStatusList.add(status);
        }

        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Scenario0:1:2,Scenario1:1:3");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        serviceRecommendHandle.realResortCore(vin, beanId, tagList, scenarioPKStatusList);

        // 验证计算完成
        for (ServiceRecommendPkStatus status : scenarioPKStatusList) {
            assertNotNull(status.getScenarioLabelScore());
            assertNotNull(status.getLabelWeight());
            assertNotNull(status.getPFeedbackLabel());
        }
    }

    // ========== realReSortLevelValue方法的分支测试 ==========

    @Test
    void testRealReSortLevelValue_ArrayValuesIsNull() {
        // 测试arrayValues为null的分支
        List<ServiceRecommendPkStatus> serviceRecommendPkStatusList = buildValidCollectList();
        List<String> scenarioCodes = Arrays.asList("Ep0EvjY");

        // 模拟Redis查询异常，返回null
        when(jedis.hmget(anyString(), any(String[].class))).thenThrow(new RuntimeException("Redis error"));

        serviceRecommendHandle.realReSortLevelValue(serviceRecommendPkStatusList, scenarioCodes, jedis);

        // 验证默认值被设置
        assertEquals(300, serviceRecommendPkStatusList.get(0).getScenarioLevel());
        assertNotNull(serviceRecommendPkStatusList.get(0).getScenarioLevelScore());
    }

    @Test
    void testRealReSortLevelValue_IndexNotFound() {
        // 测试index为null的分支（scenarioCode不在列表中）
        List<ServiceRecommendPkStatus> serviceRecommendPkStatusList = new ArrayList<>();
        ServiceRecommendPkStatus status = new ServiceRecommendPkStatus();
        status.setScenarioCode("NotFoundScenario"); // 不在scenarioCodes中的code
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));
        serviceRecommendPkStatusList.add(status);

        List<String> scenarioCodes = Arrays.asList("Ep0EvjY"); // 不包含NotFoundScenario

        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(Arrays.asList("300"));

        serviceRecommendHandle.realReSortLevelValue(serviceRecommendPkStatusList, scenarioCodes, jedis);

        // 验证默认值被设置
        assertEquals(300, serviceRecommendPkStatusList.get(0).getScenarioLevel());
        assertNotNull(serviceRecommendPkStatusList.get(0).getScenarioLevelScore());
    }

    @Test
    void testRealReSortLevelValue_BlankScenarioLevel() {
        // 测试arrayValues.get(index)为空的分支
        List<ServiceRecommendPkStatus> serviceRecommendPkStatusList = buildValidCollectList();
        serviceRecommendPkStatusList.get(0).setScenarioRealSortCredit(new BigDecimal("0.5"));
        List<String> scenarioCodes = Arrays.asList("Ep0EvjY");

        // 模拟Redis返回空字符串
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(Arrays.asList(""));

        serviceRecommendHandle.realReSortLevelValue(serviceRecommendPkStatusList, scenarioCodes, jedis);

        // 验证默认值被设置
        assertEquals(300, serviceRecommendPkStatusList.get(0).getScenarioLevel());
        assertNotNull(serviceRecommendPkStatusList.get(0).getScenarioLevelScore());
    }
}
