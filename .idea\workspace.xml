<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ee1ef571-4cc8-4129-b97c-903050792f27" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/controller/AnotherController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/controller/ServiceRecommendController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/controller/ServiceRecommendController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/handle/AsyncExecuteThreadPoolHandle.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/handle/AsyncExecuteThreadPoolHandle.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/handle/CalculateHandle.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/handle/ServiceRecommendHandle.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/handle/ServiceRecommendHandle.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/mapper/BusinessPrimaryJdbcTemplateService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/mapper/DataservicePrimaryJdbcTemplateService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/mapper/DataserviceSlaveJdbcTemplateService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/mapper/DataserviceSlaveJdbcTemplateService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/beantechs/bigdata/service/utils/RedisUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/beantechs/bigdata/service/handle/AsyncExecuteThreadPoolHandleTest.java" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="jar://$APPLICATION_HOME_DIR$/plugins/restClient/lib/restClient.jar!/com/intellij/ws/rest/client/requests/collection/post-requests.http" environment="test" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2ns37vb1vuC1ajPPI4zC293LArO" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.CalculateHandle.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testCalFeedbackPGaussianScore.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testCalFeedbackPTotalCredit.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testCalFeedbackPTotalCreditReset.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testQueryTodayFeedBackCurrentKeyListSize.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testQueryTodayFeedBackCurrentKeyListValue.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testRealReSortLevelValueScenarioLevel.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testRealReSortLevelValueScenarioLevelScore.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testRealResortCoreLabelWeight.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testRealResortCorePFeedbackLabel.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testRealResortCoreScenarioLabelScore.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testRealResortCoreScenarioRealSortCredit.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetLabelNumPKParamTagsMatchListContainsS000001.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetLabelNumPKParamTagsMatchListContainsS000002.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetLabelNumPKParamTagsMatchListSize.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetLabelNumPKParamTagsMatchNum.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetLabelNumPKParamTagsSumNum.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetTriggerParamAdvanceComputeScore.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetTriggerParamContainsS000001.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetTriggerParamContainsS000002.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetTriggerParamResultNotNull.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetTriggerParamScenarioCode.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ServiceRecommendHandleTest.testSetTriggerParamTagListContains.executor&quot;: &quot;Run&quot;,
    &quot;Maven. [org.apache.maven.plugins:maven-archetype-plugin:RELEASE:generate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.smart-audio-api-service [cn.smallbun.screw:screw-maven-plugin:1.0.5:run].executor&quot;: &quot;Run&quot;,
    &quot;Maven.smart-audio-api-service [org.apache.maven.plugins:maven-clean-plugin:3.2.0:clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.smart-audio-api-service [org.jacoco:jacoco-maven-plugin:0.8.13:prepare-agent].executor&quot;: &quot;Run&quot;,
    &quot;Maven.smart-audio-api-service [org.jacoco:jacoco-maven-plugin:0.8.13:report-integration].executor&quot;: &quot;Run&quot;,
    &quot;Maven.smart-audio-api-service [org.jacoco:jacoco-maven-plugin:0.8.13:report].executor&quot;: &quot;Run&quot;,
    &quot;Maven.smart-audio-api-service [package].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;Database detector&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.SmartAudioApiServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;develop&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Libraries&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.beantechs.bigdata.service.entity.resp" />
      <recent name="com.beantechs.bigdata.service.entity" />
      <recent name="com.beantechs.bigdata.service.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SmartAudioApiServiceApplication">
    <configuration name="ServiceRecommendHandleTest.testSetLabelNumPKParamTagsMatchListSize" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="smart-audio-api-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.beantechs.bigdata.service.handle.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.beantechs.bigdata.service.handle" />
      <option name="MAIN_CLASS_NAME" value="com.beantechs.bigdata.service.handle.ServiceRecommendHandleTest" />
      <option name="METHOD_NAME" value="testSetLabelNumPKParamTagsMatchListSize" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceRecommendHandleTest.testSetTriggerParamAdvanceComputeScore" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="smart-audio-api-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.beantechs.bigdata.service.handle.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.beantechs.bigdata.service.handle" />
      <option name="MAIN_CLASS_NAME" value="com.beantechs.bigdata.service.handle.ServiceRecommendHandleTest" />
      <option name="METHOD_NAME" value="testSetTriggerParamAdvanceComputeScore" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceRecommendHandleTest.testSetTriggerParamScenarioCode" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="smart-audio-api-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.beantechs.bigdata.service.handle.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.beantechs.bigdata.service.handle" />
      <option name="MAIN_CLASS_NAME" value="com.beantechs.bigdata.service.handle.ServiceRecommendHandleTest" />
      <option name="METHOD_NAME" value="testSetTriggerParamScenarioCode" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceRecommendHandleTest.testSetTriggerParamTagListContains" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="smart-audio-api-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.beantechs.bigdata.service.handle.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.beantechs.bigdata.service.handle" />
      <option name="MAIN_CLASS_NAME" value="com.beantechs.bigdata.service.handle.ServiceRecommendHandleTest" />
      <option name="METHOD_NAME" value="testSetTriggerParamTagListContains" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SmartAudioApiServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="smart-audio-api-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.beantechs.bigdata.service.SmartAudioApiServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.beantechs.bigdata.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.SmartAudioApiServiceApplication" />
        <item itemvalue="JUnit.ServiceRecommendHandleTest.testSetLabelNumPKParamTagsMatchListSize" />
        <item itemvalue="JUnit.ServiceRecommendHandleTest.testSetTriggerParamTagListContains" />
        <item itemvalue="JUnit.ServiceRecommendHandleTest.testSetTriggerParamAdvanceComputeScore" />
        <item itemvalue="JUnit.ServiceRecommendHandleTest.testSetTriggerParamScenarioCode" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ee1ef571-4cc8-4129-b97c-903050792f27" name="Changes" comment="" />
      <created>1722395372276</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1722395372276</updated>
      <workItem from="1722395373123" duration="2000" />
      <workItem from="1729747793451" duration="5000" />
      <workItem from="1730437654454" duration="7000" />
      <workItem from="1733298536179" duration="1968000" />
      <workItem from="1733362747533" duration="1128000" />
      <workItem from="1733375340315" duration="4366000" />
      <workItem from="1733449005408" duration="1480000" />
      <workItem from="1733462061290" duration="2762000" />
      <workItem from="1733720765596" duration="1189000" />
      <workItem from="1733796932356" duration="1545000" />
      <workItem from="1733807345879" duration="1193000" />
      <workItem from="1733990583881" duration="86000" />
      <workItem from="1734068208071" duration="1325000" />
      <workItem from="1734165532111" duration="17000" />
      <workItem from="1735023332641" duration="601000" />
      <workItem from="1735216027208" duration="789000" />
      <workItem from="1735297705111" duration="218000" />
      <workItem from="1735548084590" duration="616000" />
      <workItem from="1735549451910" duration="3000" />
      <workItem from="1735549790036" duration="489000" />
      <workItem from="1735552803082" duration="34000" />
      <workItem from="1735609323363" duration="621000" />
      <workItem from="1735782022641" duration="1792000" />
      <workItem from="1737176933828" duration="10000" />
      <workItem from="1738827971836" duration="3680000" />
      <workItem from="1738894749623" duration="1252000" />
      <workItem from="1738896694075" duration="3118000" />
      <workItem from="1739439561792" duration="10000" />
      <workItem from="1739947257837" duration="8437000" />
      <workItem from="1740971890442" duration="3616000" />
      <workItem from="1741067736276" duration="985000" />
      <workItem from="1742793490894" duration="4732000" />
      <workItem from="1742809987080" duration="9903000" />
      <workItem from="1742954060315" duration="46324000" />
      <workItem from="1743474544892" duration="41703000" />
      <workItem from="1744338331267" duration="21455000" />
      <workItem from="1744699043540" duration="8479000" />
      <workItem from="1744795054506" duration="20000" />
      <workItem from="1744795104124" duration="37000" />
      <workItem from="1744795160515" duration="3683000" />
      <workItem from="1745399761641" duration="595000" />
      <workItem from="1745401271273" duration="6635000" />
      <workItem from="1745825776457" duration="349000" />
      <workItem from="1747027068209" duration="1752000" />
      <workItem from="1747277354296" duration="4792000" />
      <workItem from="1747382917655" duration="11306000" />
      <workItem from="1748314737935" duration="9559000" />
      <workItem from="1754462879652" duration="21242000" />
      <workItem from="1754632123383" duration="5335000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName=".gitlab-ci.yml" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>