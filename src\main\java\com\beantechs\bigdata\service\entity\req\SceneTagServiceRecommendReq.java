package com.beantechs.bigdata.service.entity.req;

import com.beantechs.bigdata.service.entity.SceneTagServiceRecommendTreeTag;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SceneTagServiceRecommendReq {


    @NotNull
    private String vin;

    @NotNull
    private String beanId;


    @NotNull
    private List<String> tagList;

    @NotNull
    private List<SceneTagServiceRecommendTreeTag> treeTagList;

    private String executedTreeCode;
}
