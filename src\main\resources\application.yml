spring:
  profiles:
    active: sit
  application:
    name: smart-audio-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 60000
app:
  id: smart-audio-service
apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
logging:
  level:
    root: warn
springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  swagger-ui:
    path: swagger
  packages-to-scan: com.beantechs.bigdata.service.controller