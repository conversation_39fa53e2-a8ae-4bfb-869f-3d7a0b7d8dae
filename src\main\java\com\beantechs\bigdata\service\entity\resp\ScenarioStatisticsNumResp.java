package com.beantechs.bigdata.service.entity.resp;


import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
/**
 * <AUTHOR>
 */
@Data
public class ScenarioStatisticsNumResp implements Serializable {
    @Column("scenario_id")
    private Integer scenarioId;

    @Column("scenario_code")
    private String scenarioCode;

    @Column("call_num")
    private Integer callNum;

    @Column("success_num")
    private Integer successNum;

    @Column("scenario_total_credit")
    private BigDecimal scenarioTotalCredit;

    @Column("scenario_dest_credit")
    private BigDecimal scenarioDestCredit;

    @Column("total_pv")
    private Integer totalPv;

    private List<ScenarioStatisticsNumSbroadcast> pv;

    private List<ScenarioStatisticsNumSbroadcast> uv;

    @Column("call_num_action_pk_cloud")
    private Integer callNumActionPkCloud;

    @Column("success_num_action_pk_cloud")
    private Integer successNumActionPkCloud;

    @Column("call_num_action_pk_car")
    private Integer callNumActionPkCar;

    @Column("success_num_action_pk_car")
    private Integer successNumActionPkCar;

    @Column("action_sign")
    private Integer actionSign;
}
