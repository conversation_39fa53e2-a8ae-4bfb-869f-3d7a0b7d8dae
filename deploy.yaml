apiVersion: K8S_API_VERSION
kind: Deployment
metadata:
  name: smart-audio-api-service
  namespace: default
spec:
  replicas: REPLICASNUM
  strategy:
    rollingUpdate:
      maxSurge: 25%       # 一次可以添加多少个Pod，最少1个
      maxUnavailable: 25% # 滚动更新期间最大多少个Pod不可用，最少0个
  selector:
    matchLabels:
      k8s-app: smart-audio-api-service
  template:
    metadata:
      labels:
        k8s-app: smart-audio-api-service
    spec:
      initContainers:
        - name: init-agent
          image: SKYWALKING_IMAGE
          imagePullPolicy: Always
          command: [ "/app/init-agent" ]
          args: [ "" ]
          volumeMounts:
            - name: sw-agent
              mountPath: /opt/skywalking
      volumes:
        - name: sw-agent
          emptyDir: { }
      containers:
        - name: smart-audio-api-service
          image: DOCKER_IMAGE
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          volumeMounts:
            - name: sw-agent
              mountPath: /opt/skywalking
          #容器运行前需设置的环境变量列表
          env:
            - name: JAVA_TOOL_OPTIONS
              value: -javaagent:/opt/skywalking/agent/skywalking-agent.jar
            - name: SW_AGENT_NAME
              value: smart-audio-api-service
            - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
              value: skywalking-svc.default:11800
            - name: SPRING_PROFILES_ACTIVE
              value: ACTIVE_PROFILE
            - name: JAVA_OPTS
              value: "-XX:+UseContainerSupport"
            - name: aliyun_logs_smart-audio-api-service
              value: stdout
          # 存活检查：kubernetes认为该pod是存活的,不存活则需要重启
          livenessProbe:
            tcpSocket:
              port: 8080
            # equals to the maximum startup time of the application + couple of seconds
            initialDelaySeconds: 900
            periodSeconds: 30
          # 就绪检查：kubernetes认为该pod是启动成功的
          readinessProbe:
            tcpSocket:
              port: 8080
            ## equals to minimum startup time of the application
            initialDelaySeconds: 35
            periodSeconds: 30
          # 资源限制 keep request = limit to keep this container in guaranteed class
          resources:
            requests:
              cpu: RESOURCES_CPU
              memory: RESOURCES_MEMORY
            limits:
              cpu: RESOURCES_LIMIT_CPU
              memory: RESOURCES_LIMIT_MEMORY
