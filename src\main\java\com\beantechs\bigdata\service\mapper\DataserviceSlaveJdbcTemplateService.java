package com.beantechs.bigdata.service.mapper;

import com.beantechs.bigdata.service.entity.*;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import com.beantechs.bigdata.service.entity.resp.ScenarioStatisticsNumResp;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
/**
 * <AUTHOR>
 */
@Repository
public class DataserviceSlaveJdbcTemplateService {


    private final JdbcTemplate jdbcTemplateDataserviceSlave;

    public DataserviceSlaveJdbcTemplateService(@Qualifier("jdbcTemplateDataserviceSlave") JdbcTemplate jdbcTemplateDataserviceSlave) {
        this.jdbcTemplateDataserviceSlave = jdbcTemplateDataserviceSlave;
    }


    public ScenarioTagServiceRecommendationDo getScenarioOne(String vin, String beanId, String dt) {
        String sql = """
                SELECT
                    created_at,
                    bean_id,
                    vin,
                    scenario_code_ranks,
                    dt
                FROM
                    ai_dwd_scenario_tag_service_combine_recommendation_to_mysql_df
                WHERE
                    vin = ? AND bean_id = ? AND dt = ?
                """;
        try {
            List<String> params = new ArrayList<>();
            params.add(vin);
            params.add(beanId);
            params.add(dt);

            return jdbcTemplateDataserviceSlave.queryForObject(
                    sql,
                    new BeanPropertyRowMapper<>(ScenarioTagServiceRecommendationDo.class),
                    params.toArray()
            );
        } catch (Exception e) {
            return null;
        }
    }



}
