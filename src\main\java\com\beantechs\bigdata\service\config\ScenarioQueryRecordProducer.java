package com.beantechs.bigdata.service.config;

import com.alibaba.fastjson.JSON;
import com.beantechs.bigdata.service.entity.ScenarioQueryRecord;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScenarioQueryRecordProducer {


    @Value("${smart.audio.pk.result.handle.produce.topic}")
    private String pkResultHandleTopic;

    private final KafkaTemplate<String, String> kafkaTemplate;

    public ScenarioQueryRecordProducer(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    public void send(List<ScenarioQueryRecord> recordList) {
        final String jsonString = JSON.toJSONString(recordList);

        if (log.isDebugEnabled()) {
            log.warn("准备发送消息为：{},topic:{}", jsonString, pkResultHandleTopic);
        }

        // 发送消息
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(pkResultHandleTopic, jsonString);

        future.addCallback(new ListenableFutureCallback<>() {
            @Override
            public void onFailure(@NotNull Throwable throwable) {
                log.error(pkResultHandleTopic + " - 生产者 发送消息失败：{}", throwable.getMessage());
            }
            @Override
            public void onSuccess(SendResult<String, String> sendResult) {
            }
        });
    }


}
