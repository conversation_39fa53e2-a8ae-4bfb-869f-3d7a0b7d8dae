# JaCoCo分支覆盖率提升方案

## 概述
为了将`ServiceRecommendHandle`类的JaCoCo Missed Branches覆盖率从55%提升到80%以上，我们进行了全面的分支测试补充。

## 当前JaCoCo配置
在`pom.xml`中已正确配置JaCoCo插件，只对`ServiceRecommendHandle*`类进行插桩统计：

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.13</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
            <configuration>
                <includes>
                    <include>com/beantechs/bigdata/service/handle/ServiceRecommendHandle*</include>
                </includes>
            </configuration>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
            <configuration>
                <includes>
                    <include>com/beantechs/bigdata/service/handle/ServiceRecommendHandle*</include>
                </includes>
            </configuration>
        </execution>
    </executions>
</plugin>
```

## 分支覆盖率分析

### 原有测试覆盖的分支
- 基本的方法调用路径
- 正常的业务流程
- 部分静态方法测试

### 缺失的关键分支
1. **异常处理分支**：Redis连接异常、查询异常等
2. **边界条件分支**：空集合、null值、边界值等
3. **条件判断分支**：各种if-else条件的不同路径
4. **循环和过滤分支**：集合操作中的条件分支

## 新增测试用例

### 1. resetRequestId方法分支测试
- `testResetRequestId_ExtendIsNull()`: 测试extend为null的分支
- `testResetRequestId_ExtendNotNullButRequestIdIsBlank()`: 测试requestId为空的分支
- `testResetRequestId_ExtendNotNullButRequestIdIsNull()`: 测试requestId为null的分支
- `testResetRequestId_ExtendNotNullAndRequestIdNotBlank()`: 测试正常requestId的分支

### 2. setTriggerParam方法分支测试
- `testSetTriggerParam_ItemTagListIsEmpty()`: 测试item.getTagList()为空的分支
- `testSetTriggerParam_TriggerTypeListIsEmpty()`: 测试triggerTypeList为空的分支
- `testSetTriggerParam_ItemTagListNotEmpty()`: 测试item.getTagList()不为空的分支

### 3. calFeedbackP方法分支测试
- `testCalFeedbackP_ServiceRecommendPKStatusIsNull()`: 测试status为null的分支
- `testCalFeedbackP_CurrentValuesIsEmpty()`: 测试currentValues为空的分支
- `testCalFeedbackP_CurrentValuesIsNull()`: 测试currentValues为null的分支
- `testCalFeedbackP_CurrentPIsBlank()`: 测试currentP为空字符串的分支
- `testCalFeedbackP_FeedbackValueLessThanNegativeOne()`: 测试feedbackValue < -1的边界条件
- `testCalFeedbackP_FeedbackValueGreaterThanTwo()`: 测试feedbackValue > 2的边界条件

### 4. 主方法serviceRecommendHandle分支测试
- `testServiceRecommendHandle_EmptyRedisCollect()`: 测试Redis返回空集合
- `testServiceRecommendHandle_RedisCollectWithNullValues()`: 测试Redis返回包含null值的集合
- `testServiceRecommendHandle_EmptyNotEmptyRedisCollect()`: 测试过滤后为空的集合
- `testServiceRecommendHandle_FeedbackQueryException()`: 测试查询反馈分异常
- `testServiceRecommendHandle_FilteredValidCollectEmpty()`: 测试过滤后validCollect为空

### 5. sortAlreadyExecuted方法分支测试
- `testSortAlreadyExecuted_RedisException()`: 测试Redis查询异常
- `testSortAlreadyExecuted_WithExecutedScenarios()`: 测试包含已执行剧本的分支

### 6. realResortCore方法分支测试
- `testRealResortCore_CombinIsNull()`: 测试combin为null的分支
- `testRealResortCore_InvalidListNotEmpty()`: 测试invalidList不为空的分支
- `testRealResortCore_EqualPFeedbackLabel()`: 测试相同pFeedbackLabel的排序分支

### 7. realReSortLevelValue方法分支测试
- `testRealReSortLevelValue_ArrayValuesIsNull()`: 测试arrayValues为null的分支
- `testRealReSortLevelValue_IndexNotFound()`: 测试index为null的分支
- `testRealReSortLevelValue_BlankScenarioLevel()`: 测试scenarioLevel为空的分支

## 测试文件结构

### 现有测试文件
- `ServiceRecommendHandleTest.java`: 原有的基础测试 + 新增的分支测试
- `ServiceRecommendHandleServiceMethodTest.java`: 服务方法的集成测试

### 新增测试文件
- `ServiceRecommendHandleBranchTest.java`: 专门用于分支覆盖率测试的新文件

## 预期效果

通过这些新增的测试用例，预期能够：

1. **覆盖异常处理分支**：提升约15-20%的分支覆盖率
2. **覆盖边界条件分支**：提升约10-15%的分支覆盖率
3. **覆盖条件判断分支**：提升约10-15%的分支覆盖率

总计预期将分支覆盖率从55%提升到80%以上。

## 运行测试

解决依赖问题后，可以运行以下命令来生成覆盖率报告：

```bash
mvn clean test jacoco:report
```

覆盖率报告将生成在：
- `target/site/jacoco/index.html` - HTML格式报告
- `target/site/jacoco/jacoco.xml` - XML格式数据

## 注意事项

1. **依赖问题**：当前项目存在`common2:2.17.3`依赖无法解析的问题，需要先解决依赖问题才能运行测试
2. **Mock配置**：所有测试都使用了适当的Mock配置，避免对外部依赖的实际调用
3. **边界值测试**：特别关注了数值计算中的边界条件，如负值、超出范围值等
4. **异常处理**：确保所有可能的异常分支都有对应的测试覆盖

## 建议

1. 解决依赖问题后立即运行测试验证覆盖率提升效果
2. 如果覆盖率仍未达到80%，可以进一步分析JaCoCo报告，识别剩余的未覆盖分支
3. 考虑添加更多的集成测试来覆盖复杂的业务场景
4. 定期运行覆盖率测试，确保新代码也保持高覆盖率
