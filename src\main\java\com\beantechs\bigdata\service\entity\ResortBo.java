package com.beantechs.bigdata.service.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResortBo implements Serializable {

    private String treeCode;

    private Integer num;

    private Integer rank;

    private double resultPvalue;


    public ResortBo(String treeCode, Integer num, Integer rank) {
        this.treeCode = treeCode;
        this.num = num;
        this.rank = rank;
    }

}
