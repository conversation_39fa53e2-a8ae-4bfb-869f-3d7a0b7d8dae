package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioTagServiceRecommendationDo;
import com.beantechs.bigdata.service.entity.ServiceRecommendPkStatus;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendReq;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendScenarioTag;
import com.beantechs.bigdata.service.entity.resp.ServiceRecommendResp;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import com.beantechs.service.ResBody.ResponseEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class ServiceRecommendHandleTest {


    @Mock
    private DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;

    @Mock
    private JedisPool jedisPool;

    @Mock
    private Jedis jedis;

    @Mock
    private AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    @InjectMocks
    private ServiceRecommendHandle serviceRecommendHandle;

    private ScenarioRecommendReq req;
    private List<ServiceRecommendPkStatus> validCollect;
    private List<String> tagList;
    private List<String> notEmptyRedisCollect;
    private List<ScenarioRecommendScenarioTag> scenarioTagList;
    private String todayStr;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(jedisPool.getResource()).thenReturn(jedis);

        // 准备测试请求数据
        req = new ScenarioRecommendReq();
        req.setVin("LGW6ER5HJ09744403");
        req.setBeanId("5639227463345668133");

        // 准备标签列表
        tagList = new ArrayList<>();
        tagList.add("S000001");
        tagList.add("S000002");
        req.setTagList(new ArrayList<>(tagList));

        // 准备剧本标签列表
        scenarioTagList = new ArrayList<>();
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(List.of("S000001"));
        scenarioTagList.add(scenarioTag);
        req.setScenarioTagList(scenarioTagList);

        req.setExecutedScenarioCode("");

        JSONObject extend = new JSONObject();
        req.setExtend(extend);

        // 准备 validCollect
        validCollect = new ArrayList<>();
        ServiceRecommendPkStatus status = new ServiceRecommendPkStatus();
        status.setScenarioCode("Ep0EvjY");
        status.setTagList(List.of("S000001"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));
        validCollect.add(status);

        // 准备 Redis 返回数据
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));

        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);

        notEmptyRedisCollect = new ArrayList<>();
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 当前日期
        todayStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Test
    void testSetTriggerParamResultNotNull() {
        // 通过反射调用私有静态方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    void testSetTriggerParamContainsS000001() {
        // 通过反射调用私有静态方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertTrue(result.contains("S000001"));
    }

    @Test
    void testSetTriggerParamContainsS000002() {
        // 通过反射调用私有静态方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertTrue(result.contains("S000002"));
    }

    @Test
    void testSetTriggerParamScenarioCode() {
        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
    }

    @Test
    void testSetTriggerParamAdvanceComputeScore() {
        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertEquals(new BigDecimal("0.7"), validCollect.get(0).getAdvanceComputeScore());
    }

    @Test
    void testSetTriggerParamTagListContains() {
        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertTrue(validCollect.get(0).getTagList().contains("S000001"));
    }

    @Test
    void testSetLabelNumPKParamTagsMatchNum() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertEquals(2, validCollect.get(0).getTagsMatchNum());
    }

    @Test
    void testSetLabelNumPKParamTagsSumNum() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertEquals(2, validCollect.get(0).getTagsSumNum());
    }

    @Test
    void testSetLabelNumPKParamTagsMatchListSize() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertEquals(2, validCollect.get(0).getTagsMatchList().size());
    }

    @Test
    void testSetLabelNumPKParamTagsMatchListContainsS000001() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertTrue(validCollect.get(0).getTagsMatchList().contains("S000001"));
    }

    @Test
    void testSetLabelNumPKParamTagsMatchListContainsS000002() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertTrue(validCollect.get(0).getTagsMatchList().contains("S000002"));
    }

    @Test
    void testQueryTodayFeedBackCurrentKeyListSize() {
        List<String> codes = List.of("Ep0EvjY");
        List<String> currentKeyList = new ArrayList<>();

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "queryTodayFeedBack",
                req.getVin(),
                req.getBeanId(),
                codes,
                currentKeyList,
                todayStr);

        // 验证结果
        assertEquals(1, currentKeyList.size());
    }

    @Test
    void testQueryTodayFeedBackCurrentKeyListValue() {
        List<String> codes = List.of("Ep0EvjY");
        List<String> currentKeyList = new ArrayList<>();

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "queryTodayFeedBack",
                req.getVin(),
                req.getBeanId(),
                codes,
                currentKeyList,
                todayStr);

        // 验证结果
        assertEquals("SMART_AUDIO:PVALUE:LGW6ER5HJ09744403:5639227463345668133:Ep0EvjY:" + todayStr, currentKeyList.get(0));
    }

    @Test
    void testRealResortCoreScenarioLabelScore() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getScenarioLabelScore());
    }

    @Test
    void testRealResortCoreLabelWeight() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getLabelWeight());
    }

    @Test
    void testRealResortCorePFeedbackLabel() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getPFeedbackLabel());
    }

    @Test
    void testRealResortCoreScenarioRealSortCredit() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getScenarioRealSortCredit());
    }

    @Test
    void testRealReSortLevelValueScenarioLevel() {
        // 模拟 Redis 查询返回
        List<String> scenarioCodes = List.of("aeqgkaR");
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(List.of("300"));

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));

        // 调用方法
        serviceRecommendHandle.realReSortLevelValue(validCollect, scenarioCodes, jedis);

        // 验证结果
        assertEquals(300, validCollect.get(0).getScenarioLevel());
    }

    @Test
    void testRealReSortLevelValueScenarioLevelScore() {
        // 模拟 Redis 查询返回
        List<String> scenarioCodes = List.of("aeqgkaR");
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(List.of("300"));

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));

        // 调用方法
        serviceRecommendHandle.realReSortLevelValue(validCollect, scenarioCodes, jedis);

        // 验证结果
        assertNotNull(validCollect.get(0).getScenarioLevelScore());
    }

    @Test
    void testRealReSortLevelValueScenarioRealSortCredit() {
        // 模拟 Redis 查询返回
        List<String> scenarioCodes = List.of("aeqgkaR");
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(List.of("300"));

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));

        // 调用方法
        serviceRecommendHandle.realReSortLevelValue(validCollect, scenarioCodes, jedis);

        // 验证结果
        assertTrue(validCollect.get(0).getScenarioRealSortCredit().compareTo(new BigDecimal("0.5")) > 0);
    }

    @Test
    void testCalFeedbackPTotalCredit() {
        // 设置测试数据
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
    }

    @Test
    void testCalFeedbackPTotalCreditReset() {
        // 设置测试数据
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCreditReset());
    }

    @Test
    void testCalFeedbackPGaussianScore() {
        // 设置测试数据
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getGaussianScore());
    }

    // ========== resetRequestId方法的分支测试 ==========

    @Test
    void testResetRequestId_ExtendIsNull() {
        // 测试extend为null的分支
        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                (JSONObject) null);

        assertNotNull(result);
        assertEquals(32, result.length()); // UUID去掉"-"后的长度
    }

    @Test
    void testResetRequestId_ExtendNotNullButRequestIdIsBlank() {
        // 测试extend不为null但requestId为空的分支
        JSONObject extend = new JSONObject();
        extend.put("requestId", "");

        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                extend);

        assertNotNull(result);
        assertEquals(32, result.length()); // UUID去掉"-"后的长度
    }

    @Test
    void testResetRequestId_ExtendNotNullButRequestIdIsNull() {
        // 测试extend不为null但requestId为null的分支
        JSONObject extend = new JSONObject();
        extend.put("requestId", (String) null);

        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                extend);

        assertNotNull(result);
        assertEquals(32, result.length()); // UUID去掉"-"后的长度
    }

    @Test
    void testResetRequestId_ExtendNotNullAndRequestIdNotBlank() {
        // 测试extend不为null且requestId不为空的分支
        JSONObject extend = new JSONObject();
        extend.put("requestId", "test-request-id-123");

        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                extend);

        assertEquals("test-request-id-123", result);
    }

    // ========== setTriggerParam方法的分支测试 ==========

    @Test
    void testSetTriggerParam_ItemTagListIsEmpty() {
        // 测试item.getTagList()为空的分支
        List<String> notEmptyRedisCollect = new ArrayList<>();
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");

        // 创建剧本标签，但tagList为空
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(new ArrayList<>()); // 空的tagList
        scenarioTagList.add(scenarioTag);

        // 创建Redis数据，包含触发器类型
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 调用方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("S000001"));
        assertFalse(validCollect.isEmpty());
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
        assertTrue(validCollect.get(0).getTagList().contains("S000001"));
    }

    @Test
    void testSetTriggerParam_TriggerTypeListIsEmpty() {
        // 测试triggerTypeList为空的分支
        List<String> notEmptyRedisCollect = new ArrayList<>();
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");

        // 创建剧本标签，tagList为空
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(new ArrayList<>()); // 空的tagList
        scenarioTagList.add(scenarioTag);

        // 创建Redis数据，包含空的触发器类型（triggerType为空字符串）
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType(""); // 触发器类型为空字符串，而不是null
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 调用方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
        assertFalse(validCollect.isEmpty());
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
        // 当triggerType为空字符串时，triggerTypeList包含空字符串，所以会使用triggerTypeList而不是原始tagList
        assertTrue(validCollect.get(0).getTagList().contains(""));
    }

    @Test
    void testSetTriggerParam_ItemTagListNotEmpty() {
        // 测试item.getTagList()不为空的分支
        List<String> notEmptyRedisCollect = new ArrayList<>();
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");

        // 创建剧本标签，tagList不为空
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(Arrays.asList("S000002", "S000003")); // 不为空的tagList
        scenarioTagList.add(scenarioTag);

        // 创建Redis数据
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 调用方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
        assertFalse(validCollect.isEmpty());
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
        // 当item.getTagList()不为空时，应该使用item的tagList
        assertTrue(validCollect.get(0).getTagList().contains("S000002"));
        assertTrue(validCollect.get(0).getTagList().contains("S000003"));
    }

    // ========== calFeedbackP方法的分支测试 ==========

    @Test
    void testCalFeedbackP_ServiceRecommendPKStatusWithZeroScenarioRealSortCredit() {
        // 测试serviceRecommendPKStatus的scenarioRealSortCredit为0的分支
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(BigDecimal.ZERO); // 设置为0来测试边界条件
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_CurrentValuesIsEmpty() {
        // 测试currentValues为空的分支
        List<String> currentValues = new ArrayList<>(); // 空列表
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_CurrentValuesIsNull() {
        // 测试currentValues为null的分支
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                (List<String>) null,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_CurrentPIsBlank() {
        // 测试currentP为空字符串的分支
        List<String> currentValues = List.of(""); // 空字符串
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_FeedbackValueLessThanNegativeOne() {
        // 测试feedbackValue小于-1的边界条件
        List<String> currentValues = List.of("-2.5"); // 会导致feedbackValue < -1
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("-1.0")); // 设置一个负值
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果：feedbackValue应该被限制为-1
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_FeedbackValueGreaterThanTwo() {
        // 测试feedbackValue大于2的边界条件
        List<String> currentValues = List.of("3.0"); // 会导致feedbackValue > 2
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("1.5")); // 设置一个较大值
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果：feedbackValue应该被限制为2
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    // ========== setLabelNumPKParam方法的分支测试 ==========

    @Test
    void testSetLabelNumPKParam_EmptyTagsMatchList() {
        // 测试tagsMatchList为空的分支
        List<String> tagList = Arrays.asList("S000001", "S000002");
        ServiceRecommendPkStatus serviceRecommendPKStatus = new ServiceRecommendPkStatus();
        serviceRecommendPKStatus.setTagList(Arrays.asList("S000003", "S000004")); // 不匹配的标签

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                serviceRecommendPKStatus);

        // 验证结果：tagsMatchList应该为空
        assertTrue(serviceRecommendPKStatus.getTagsMatchList().isEmpty());
        assertEquals(0, serviceRecommendPKStatus.getTagsMatchNum());
    }

    @Test
    void testSetLabelNumPKParam_PartialMatch() {
        // 测试部分匹配的分支
        List<String> tagList = Arrays.asList("S000001", "S000002", "S000003");
        ServiceRecommendPkStatus serviceRecommendPKStatus = new ServiceRecommendPkStatus();
        serviceRecommendPKStatus.setTagList(Arrays.asList("S000001", "S000004")); // 部分匹配

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                serviceRecommendPKStatus);

        // 验证结果：只有S000001匹配
        assertEquals(1, serviceRecommendPKStatus.getTagsMatchList().size());
        assertTrue(serviceRecommendPKStatus.getTagsMatchList().contains("S000001"));
        assertEquals(1, serviceRecommendPKStatus.getTagsMatchNum());
    }

    @Test
    void testSetLabelNumPKParam_FullMatch() {
        // 测试完全匹配的分支
        List<String> tagList = Arrays.asList("S000001", "S000002");
        ServiceRecommendPkStatus serviceRecommendPKStatus = new ServiceRecommendPkStatus();
        serviceRecommendPKStatus.setTagList(Arrays.asList("S000001", "S000002")); // 完全匹配

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                serviceRecommendPKStatus);

        // 验证结果：全部匹配
        assertEquals(2, serviceRecommendPKStatus.getTagsMatchList().size());
        assertTrue(serviceRecommendPKStatus.getTagsMatchList().contains("S000001"));
        assertTrue(serviceRecommendPKStatus.getTagsMatchList().contains("S000002"));
        assertEquals(2, serviceRecommendPKStatus.getTagsMatchNum());
    }

    // ========== cannotFinalSourceHandle方法的分支测试 ==========

    @Test
    void testCannotFinalSourceHandle_SingleScenario() {
        // 测试单个剧本的fallback处理
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("TestScenario");
        scenarioTagList.add(scenarioTag);

        // 通过反射调用私有静态方法
        ServiceRecommendResp result = ReflectionTestUtils.invokeMethod(
                ServiceRecommendHandle.class,
                "cannotFinalSourceHandle",
                scenarioTagList,
                "test-request-id",
                "VIN123",
                "BEAN123");

        // 验证结果
        assertNotNull(result);
        assertEquals("VIN123", result.getVin());
        assertEquals("BEAN123", result.getBeanId());
        assertEquals(1, result.getRecommendations().size());
        assertEquals("TestScenario", result.getRecommendations().get(0));
    }

    @Test
    void testCannotFinalSourceHandle_MultipleScenarios() {
        // 测试多个剧本的fallback处理
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
            scenarioTag.setScenarioCode("Scenario" + i);
            scenarioTagList.add(scenarioTag);
        }

        // 通过反射调用私有静态方法
        ServiceRecommendResp result = ReflectionTestUtils.invokeMethod(
                ServiceRecommendHandle.class,
                "cannotFinalSourceHandle",
                scenarioTagList,
                "test-request-id",
                "VIN123",
                "BEAN123");

        // 验证结果
        assertNotNull(result);
        assertEquals("VIN123", result.getVin());
        assertEquals("BEAN123", result.getBeanId());
        assertEquals(3, result.getRecommendations().size());
        // 由于使用了Collections.shuffle，顺序可能不同，但应该包含所有剧本
        assertTrue(result.getRecommendations().contains("Scenario1"));
        assertTrue(result.getRecommendations().contains("Scenario2"));
        assertTrue(result.getRecommendations().contains("Scenario3"));
    }

    // ========== 更多边界条件测试 ==========

    @Test
    void testServiceRecommendHandle_EmptyTagList() {
        // 测试tagList为空的分支（应该返回错误）
        ScenarioRecommendReq req = new ScenarioRecommendReq();
        req.setVin("VIN123");
        req.setBeanId("BEAN123");
        req.setTagList(new ArrayList<>()); // 空的tagList
        req.setScenarioTagList(new ArrayList<>());
        req.setExecutedScenarioCode("");
        req.setExtend(new JSONObject());

        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);

        assertNotNull(result);
        // 应该返回错误信息
        assertNotEquals(200, result.getCode());
    }

    @Test
    void testServiceRecommendHandle_NullTagList() {
        // 测试tagList为null的分支（应该返回错误）
        ScenarioRecommendReq req = new ScenarioRecommendReq();
        req.setVin("VIN123");
        req.setBeanId("BEAN123");
        req.setTagList(null); // null的tagList
        req.setScenarioTagList(new ArrayList<>());
        req.setExecutedScenarioCode("");
        req.setExtend(new JSONObject());

        ResponseEntity<ServiceRecommendResp> result = serviceRecommendHandle.serviceRecommendHandle(req);

        assertNotNull(result);
        // 应该返回错误信息
        assertNotEquals(200, result.getCode());
    }

    @Test
    void testRealResortCore_TagMatchTimesMapContainsKey() {
        // 测试tagMatchTimesMap.containsKey(tag)为true的分支
        String vin = "VIN123";
        String beanId = "BEAN123";
        List<String> tagList = Arrays.asList("S000001", "S000001"); // 重复的标签
        List<ServiceRecommendPkStatus> scenarioPKStatusList = buildValidCollectList();

        // 设置必要的字段
        scenarioPKStatusList.get(0).setTagsMatchList(Arrays.asList("S000001", "S000001"));
        scenarioPKStatusList.get(0).setTagsMatchNum(2);

        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        serviceRecommendHandle.realResortCore(vin, beanId, tagList, scenarioPKStatusList);

        // 验证计算结果
        assertNotNull(scenarioPKStatusList.get(0).getScenarioLabelScore());
        assertNotNull(scenarioPKStatusList.get(0).getLabelWeight());
        assertNotNull(scenarioPKStatusList.get(0).getPFeedbackLabel());
    }

    @Test
    void testRealResortCore_SortOffLineListFiltering() {
        // 测试sortOffLineList过滤逻辑的分支
        String vin = "VIN123";
        String beanId = "BEAN123";
        List<String> tagList = Arrays.asList("S000001");
        List<ServiceRecommendPkStatus> scenarioPKStatusList = new ArrayList<>();

        // 创建多个状态，其中一些不在scenarioCodes中
        ServiceRecommendPkStatus status1 = new ServiceRecommendPkStatus();
        status1.setScenarioCode("Ep0EvjY");
        status1.setTagList(Arrays.asList("S000001"));
        status1.setTagsMatchList(Arrays.asList("S000001"));
        status1.setTagsMatchNum(1);
        scenarioPKStatusList.add(status1);

        ServiceRecommendPkStatus status2 = new ServiceRecommendPkStatus();
        status2.setScenarioCode("AnotherScenario");
        status2.setTagList(Arrays.asList("S000001"));
        status2.setTagsMatchList(Arrays.asList("S000001"));
        status2.setTagsMatchNum(1);
        scenarioPKStatusList.add(status2);

        // 模拟数据库返回包含不在当前列表中的剧本的排序信息
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2,NotInList:1:3,AnotherScenario:1:4");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        serviceRecommendHandle.realResortCore(vin, beanId, tagList, scenarioPKStatusList);

        // 验证计算完成
        for (ServiceRecommendPkStatus status : scenarioPKStatusList) {
            assertNotNull(status.getScenarioLabelScore());
            assertNotNull(status.getLabelWeight());
            assertNotNull(status.getPFeedbackLabel());
        }
    }

    @Test
    void testCalFeedbackP_EdgeCaseValues() {
        // 测试calFeedbackP方法的边界值
        List<String> currentValues = List.of("1.0"); // 边界值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("1.0"));
        status.setAdvanceComputeScore(new BigDecimal("1.0"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
        // feedbackValue应该正好等于1.0，在边界范围内
        assertTrue(status.getTotalCredit().compareTo(BigDecimal.ZERO) >= 0);
    }

    // ========== 辅助方法 ==========

    private List<ServiceRecommendPkStatus> buildValidCollectList() {
        List<ServiceRecommendPkStatus> list = new ArrayList<>();
        ServiceRecommendPkStatus status = new ServiceRecommendPkStatus();
        status.setScenarioCode("Ep0EvjY");
        status.setTagList(Arrays.asList("S000001"));
        status.setTagsMatchList(Arrays.asList("S000001"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));
        status.setGaussianScore(new BigDecimal("0.8"));
        status.setScenarioLevel(300);
        list.add(status);
        return list;
    }


}
