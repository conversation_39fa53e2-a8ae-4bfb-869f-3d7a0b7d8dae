package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioTagServiceRecommendationDo;
import com.beantechs.bigdata.service.entity.ServiceRecommendPkStatus;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendReq;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendScenarioTag;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class ServiceRecommendHandleTest {


    @Mock
    private DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;

    @Mock
    private JedisPool jedisPool;

    @Mock
    private Jedis jedis;

    @Mock
    private AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    @InjectMocks
    private ServiceRecommendHandle serviceRecommendHandle;

    private ScenarioRecommendReq req;
    private List<ServiceRecommendPkStatus> validCollect;
    private List<String> tagList;
    private List<String> notEmptyRedisCollect;
    private List<ScenarioRecommendScenarioTag> scenarioTagList;
    private String todayStr;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(jedisPool.getResource()).thenReturn(jedis);

        // 准备测试请求数据
        req = new ScenarioRecommendReq();
        req.setVin("LGW6ER5HJ09744403");
        req.setBeanId("5639227463345668133");

        // 准备标签列表
        tagList = new ArrayList<>();
        tagList.add("S000001");
        tagList.add("S000002");
        req.setTagList(new ArrayList<>(tagList));

        // 准备剧本标签列表
        scenarioTagList = new ArrayList<>();
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(List.of("S000001"));
        scenarioTagList.add(scenarioTag);
        req.setScenarioTagList(scenarioTagList);

        req.setExecutedScenarioCode("");

        JSONObject extend = new JSONObject();
        req.setExtend(extend);

        // 准备 validCollect
        validCollect = new ArrayList<>();
        ServiceRecommendPkStatus status = new ServiceRecommendPkStatus();
        status.setScenarioCode("Ep0EvjY");
        status.setTagList(List.of("S000001"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));
        validCollect.add(status);

        // 准备 Redis 返回数据
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));

        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);

        notEmptyRedisCollect = new ArrayList<>();
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 当前日期
        todayStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Test
    void testSetTriggerParamResultNotNull() {
        // 通过反射调用私有静态方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    void testSetTriggerParamContainsS000001() {
        // 通过反射调用私有静态方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertTrue(result.contains("S000001"));
    }

    @Test
    void testSetTriggerParamContainsS000002() {
        // 通过反射调用私有静态方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertTrue(result.contains("S000002"));
    }

    @Test
    void testSetTriggerParamScenarioCode() {
        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
    }

    @Test
    void testSetTriggerParamAdvanceComputeScore() {
        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertEquals(new BigDecimal("0.7"), validCollect.get(0).getAdvanceComputeScore());
    }

    @Test
    void testSetTriggerParamTagListContains() {
        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertTrue(validCollect.get(0).getTagList().contains("S000001"));
    }

    @Test
    void testSetLabelNumPKParamTagsMatchNum() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertEquals(2, validCollect.get(0).getTagsMatchNum());
    }

    @Test
    void testSetLabelNumPKParamTagsSumNum() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertEquals(2, validCollect.get(0).getTagsSumNum());
    }

    @Test
    void testSetLabelNumPKParamTagsMatchListSize() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertEquals(2, validCollect.get(0).getTagsMatchList().size());
    }

    @Test
    void testSetLabelNumPKParamTagsMatchListContainsS000001() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertTrue(validCollect.get(0).getTagsMatchList().contains("S000001"));
    }

    @Test
    void testSetLabelNumPKParamTagsMatchListContainsS000002() {
        // 设置初始标签
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagList(Arrays.asList("S000001", "S000002"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setLabelNumPKParam",
                tagList,
                validCollect);

        // 验证结果
        assertTrue(validCollect.get(0).getTagsMatchList().contains("S000002"));
    }

    @Test
    void testQueryTodayFeedBackCurrentKeyListSize() {
        List<String> codes = List.of("Ep0EvjY");
        List<String> currentKeyList = new ArrayList<>();

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "queryTodayFeedBack",
                req.getVin(),
                req.getBeanId(),
                codes,
                currentKeyList,
                todayStr);

        // 验证结果
        assertEquals(1, currentKeyList.size());
    }

    @Test
    void testQueryTodayFeedBackCurrentKeyListValue() {
        List<String> codes = List.of("Ep0EvjY");
        List<String> currentKeyList = new ArrayList<>();

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "queryTodayFeedBack",
                req.getVin(),
                req.getBeanId(),
                codes,
                currentKeyList,
                todayStr);

        // 验证结果
        assertEquals("SMART_AUDIO:PVALUE:LGW6ER5HJ09744403:5639227463345668133:Ep0EvjY:" + todayStr, currentKeyList.get(0));
    }

    @Test
    void testRealResortCoreScenarioLabelScore() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getScenarioLabelScore());
    }

    @Test
    void testRealResortCoreLabelWeight() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getLabelWeight());
    }

    @Test
    void testRealResortCorePFeedbackLabel() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getPFeedbackLabel());
    }

    @Test
    void testRealResortCoreScenarioRealSortCredit() {
        // 设置模拟返回值
        ScenarioTagServiceRecommendationDo recommendationDo = new ScenarioTagServiceRecommendationDo();
        recommendationDo.setScenarioCodeRanks("Ep0EvjY:1:2");
        when(dataserviceSlaveJdbcTemplateService.getScenarioOne(anyString(), anyString(), anyString()))
                .thenReturn(recommendationDo);

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setTagsMatchList(Arrays.asList("S000001", "S000002"));
        status.setTagsMatchNum(2);

        // 调用方法
        serviceRecommendHandle.realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);

        // 验证结果
        assertNotNull(validCollect.get(0).getScenarioRealSortCredit());
    }

    @Test
    void testRealReSortLevelValueScenarioLevel() {
        // 模拟 Redis 查询返回
        List<String> scenarioCodes = List.of("aeqgkaR");
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(List.of("300"));

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));

        // 调用方法
        serviceRecommendHandle.realReSortLevelValue(validCollect, scenarioCodes, jedis);

        // 验证结果
        assertEquals(300, validCollect.get(0).getScenarioLevel());
    }

    @Test
    void testRealReSortLevelValueScenarioLevelScore() {
        // 模拟 Redis 查询返回
        List<String> scenarioCodes = List.of("aeqgkaR");
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(List.of("300"));

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));

        // 调用方法
        serviceRecommendHandle.realReSortLevelValue(validCollect, scenarioCodes, jedis);

        // 验证结果
        assertNotNull(validCollect.get(0).getScenarioLevelScore());
    }

    @Test
    void testRealReSortLevelValueScenarioRealSortCredit() {
        // 模拟 Redis 查询返回
        List<String> scenarioCodes = List.of("aeqgkaR");
        when(jedis.hmget(anyString(), any(String[].class))).thenReturn(List.of("300"));

        // 设置初始值
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.5"));

        // 调用方法
        serviceRecommendHandle.realReSortLevelValue(validCollect, scenarioCodes, jedis);

        // 验证结果
        assertTrue(validCollect.get(0).getScenarioRealSortCredit().compareTo(new BigDecimal("0.5")) > 0);
    }

    @Test
    void testCalFeedbackPTotalCredit() {
        // 设置测试数据
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
    }

    @Test
    void testCalFeedbackPTotalCreditReset() {
        // 设置测试数据
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCreditReset());
    }

    @Test
    void testCalFeedbackPGaussianScore() {
        // 设置测试数据
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getGaussianScore());
    }

    // ========== resetRequestId方法的分支测试 ==========

    @Test
    void testResetRequestId_ExtendIsNull() {
        // 测试extend为null的分支
        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                (JSONObject) null);

        assertNotNull(result);
        assertEquals(32, result.length()); // UUID去掉"-"后的长度
    }

    @Test
    void testResetRequestId_ExtendNotNullButRequestIdIsBlank() {
        // 测试extend不为null但requestId为空的分支
        JSONObject extend = new JSONObject();
        extend.put("requestId", "");

        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                extend);

        assertNotNull(result);
        assertEquals(32, result.length()); // UUID去掉"-"后的长度
    }

    @Test
    void testResetRequestId_ExtendNotNullButRequestIdIsNull() {
        // 测试extend不为null但requestId为null的分支
        JSONObject extend = new JSONObject();
        extend.put("requestId", (String) null);

        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                extend);

        assertNotNull(result);
        assertEquals(32, result.length()); // UUID去掉"-"后的长度
    }

    @Test
    void testResetRequestId_ExtendNotNullAndRequestIdNotBlank() {
        // 测试extend不为null且requestId不为空的分支
        JSONObject extend = new JSONObject();
        extend.put("requestId", "test-request-id-123");

        String result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "resetRequestId",
                extend);

        assertEquals("test-request-id-123", result);
    }

    // ========== setTriggerParam方法的分支测试 ==========

    @Test
    void testSetTriggerParam_ItemTagListIsEmpty() {
        // 测试item.getTagList()为空的分支
        List<String> notEmptyRedisCollect = new ArrayList<>();
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");

        // 创建剧本标签，但tagList为空
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(new ArrayList<>()); // 空的tagList
        scenarioTagList.add(scenarioTag);

        // 创建Redis数据，包含触发器类型
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 调用方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("S000001"));
        assertFalse(validCollect.isEmpty());
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
        assertTrue(validCollect.get(0).getTagList().contains("S000001"));
    }

    @Test
    void testSetTriggerParam_TriggerTypeListIsEmpty() {
        // 测试triggerTypeList为空的分支
        List<String> notEmptyRedisCollect = new ArrayList<>();
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");

        // 创建剧本标签，tagList为空
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(new ArrayList<>()); // 空的tagList
        scenarioTagList.add(scenarioTag);

        // 创建Redis数据，包含空的触发器类型（triggerType为空字符串）
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType(""); // 触发器类型为空字符串，而不是null
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 调用方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
        assertFalse(validCollect.isEmpty());
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
        // 当triggerTypeList为空时，应该使用原始的tagList
        assertTrue(validCollect.get(0).getTagList().contains("S000001"));
    }

    @Test
    void testSetTriggerParam_ItemTagListNotEmpty() {
        // 测试item.getTagList()不为空的分支
        List<String> notEmptyRedisCollect = new ArrayList<>();
        List<ScenarioRecommendScenarioTag> scenarioTagList = new ArrayList<>();
        List<ServiceRecommendPkStatus> validCollect = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        tagList.add("S000001");

        // 创建剧本标签，tagList不为空
        ScenarioRecommendScenarioTag scenarioTag = new ScenarioRecommendScenarioTag();
        scenarioTag.setScenarioCode("Ep0EvjY");
        scenarioTag.setTagList(Arrays.asList("S000002", "S000003")); // 不为空的tagList
        scenarioTagList.add(scenarioTag);

        // 创建Redis数据
        Scenario scenario = new Scenario();
        scenario.setScenarioCode("Ep0EvjY");
        scenario.setTriggerType("S000001");
        scenario.setScenarioTotalCredit(new BigDecimal("0.7"));
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioList.add(scenario);
        notEmptyRedisCollect.add(JSON.toJSONString(scenarioList));

        // 调用方法
        List<String> result = ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "setTriggerParam",
                notEmptyRedisCollect,
                scenarioTagList,
                validCollect,
                tagList);

        // 验证结果
        assertNotNull(result);
        assertFalse(validCollect.isEmpty());
        assertEquals("Ep0EvjY", validCollect.get(0).getScenarioCode());
        // 当item.getTagList()不为空时，应该使用item的tagList
        assertTrue(validCollect.get(0).getTagList().contains("S000002"));
        assertTrue(validCollect.get(0).getTagList().contains("S000003"));
    }

    // ========== calFeedbackP方法的分支测试 ==========

    @Test
    void testCalFeedbackP_ServiceRecommendPKStatusWithZeroScenarioRealSortCredit() {
        // 测试serviceRecommendPKStatus的scenarioRealSortCredit为0的分支
        List<String> currentValues = List.of("0.3");
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(BigDecimal.ZERO); // 设置为0来测试边界条件
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_CurrentValuesIsEmpty() {
        // 测试currentValues为空的分支
        List<String> currentValues = new ArrayList<>(); // 空列表
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_CurrentValuesIsNull() {
        // 测试currentValues为null的分支
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                (List<String>) null,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_CurrentPIsBlank() {
        // 测试currentP为空字符串的分支
        List<String> currentValues = List.of(""); // 空字符串
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("0.4"));
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_FeedbackValueLessThanNegativeOne() {
        // 测试feedbackValue小于-1的边界条件
        List<String> currentValues = List.of("-2.5"); // 会导致feedbackValue < -1
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("-1.0")); // 设置一个负值
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果：feedbackValue应该被限制为-1
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }

    @Test
    void testCalFeedbackP_FeedbackValueGreaterThanTwo() {
        // 测试feedbackValue大于2的边界条件
        List<String> currentValues = List.of("3.0"); // 会导致feedbackValue > 2
        ServiceRecommendPkStatus status = validCollect.get(0);
        status.setScenarioRealSortCredit(new BigDecimal("1.5")); // 设置一个较大值
        status.setAdvanceComputeScore(new BigDecimal("0.7"));

        // 通过反射调用私有静态方法
        ReflectionTestUtils.invokeMethod(serviceRecommendHandle,
                "calFeedbackP",
                currentValues,
                0,
                status);

        // 验证结果：feedbackValue应该被限制为2
        assertNotNull(status.getTotalCredit());
        assertNotNull(status.getTotalCreditReset());
        assertNotNull(status.getGaussianScore());
    }


}
